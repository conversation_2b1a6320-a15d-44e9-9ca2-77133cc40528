import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { queryKeys } from '@/lib/react-query'
import { Post, Category } from '@/types'

interface PostsQueryParams {
  page?: number
  limit?: number
  searchTerm?: string
  category?: string
  status?: string
}

interface PostsResponse {
  posts: Post[]
  count: number
  totalPages: number
}

// Fetch posts with caching
export function usePostsQuery(params: PostsQueryParams = {}) {
  return useQuery({
    queryKey: queryKeys.posts.list(params),
    queryFn: async (): Promise<PostsResponse> => {
      const searchParams = new URLSearchParams({
        page: (params.page || 1).toString(),
        limit: (params.limit || 10).toString(),
        searchTerm: params.searchTerm || '',
        category: params.category || 'all',
        status: params.status || 'all',
      })

      const response = await fetch(`/api/posts?${searchParams.toString()}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch posts')
      }

      return response.json()
    },
    // Enable background refetching for better UX
    refetchOnWindowFocus: true,
    // Stale time of 2 minutes for posts (they change frequently)
    staleTime: 2 * 60 * 1000,
  })
}

// Fetch single post
export function usePostQuery(id: string) {
  return useQuery({
    queryKey: queryKeys.posts.detail(id),
    queryFn: async (): Promise<Post> => {
      const response = await fetch(`/api/posts/${id}`, {
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch post')
      }

      const data = await response.json()
      return data.post
    },
    enabled: !!id,
    // Cache individual posts for longer since they don't change as often
    staleTime: 5 * 60 * 1000,
  })
}

// Fetch categories with caching
export function useCategoriesQuery() {
  return useQuery({
    queryKey: queryKeys.categories.list(),
    queryFn: async (): Promise<Category[]> => {
      const response = await fetch('/api/categories', {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Failed to fetch categories')
      }

      const data = await response.json()
      return data.categories || []
    },
    // Categories change rarely, cache for longer
    staleTime: 10 * 60 * 1000,
  })
}

// Delete post mutation
export function useDeletePostMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (postId: string) => {
      const response = await fetch(`/api/posts/${postId}`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete post')
      }

      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch posts queries
      queryClient.invalidateQueries({ queryKey: queryKeys.posts.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.all })
    },
  })
}

// Create/Update post mutation
export function usePostMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, data }: { id?: string; data: any }) => {
      const url = id ? `/api/posts/${id}` : '/api/posts'
      const method = id ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to ${id ? 'update' : 'create'} post`)
      }

      return response.json()
    },
    onSuccess: (data, variables) => {
      // Invalidate posts queries
      queryClient.invalidateQueries({ queryKey: queryKeys.posts.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.all })
      
      // Update the specific post in cache if it was an update
      if (variables.id && data.post) {
        queryClient.setQueryData(queryKeys.posts.detail(variables.id), data.post)
      }
    },
  })
}
