'use client'

import dynamic from 'next/dynamic'
import { Suspense, useCallback, useRef, useEffect, useState } from 'react'
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Link,
  Image,
  Code,
  Quote,
  Heading1,
  Heading2,
  Heading3,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Undo,
  Redo,
  Eye,
  EyeOff,
  Type,
  Palette,
  Table,
  Video,
  FileText,
  Hash
} from 'lucide-react'
import LoadingSpinner from './LoadingSpinner'

// Dynamically import ReactQuill with loading state
const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => (
    <div className="h-96 bg-white border border-gray-300 rounded-lg flex items-center justify-center">
      <LoadingSpinner text="Loading professional editor..." />
    </div>
  )
})

// Dynamically import Quill modules
const QuillBetterTable = dynamic(() => import('quill-better-table'), { ssr: false })
const QuillImageResize = dynamic(() => import('quill-image-resize-module-react'), { ssr: false })

// Dynamically import the CSS to reduce initial bundle size
dynamic(() => import('react-quill/dist/quill.snow.css'), { ssr: false })
dynamic(() => import('quill-better-table/dist/quill-better-table.css'), { ssr: false })

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  height?: string
  showWordCount?: boolean
  showReadingTime?: boolean
  enableTableOfContents?: boolean
  onTableOfContentsChange?: (toc: any[]) => void
}

export default function RichTextEditor({
  value,
  onChange,
  placeholder = "Start writing your professional blog post...",
  height = "500px",
  showWordCount = true,
  showReadingTime = true,
  enableTableOfContents = true,
  onTableOfContentsChange
}: RichTextEditorProps) {
  const quillRef = useRef<any>(null)
  const [wordCount, setWordCount] = useState(0)
  const [readingTime, setReadingTime] = useState(0)
  const [tableOfContents, setTableOfContents] = useState<any[]>([])
  const [isPreviewMode, setIsPreviewMode] = useState(false)

  // Professional toolbar configuration
  const modules = {
    toolbar: {
      container: [
        // Text formatting
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'font': [] }, { 'size': ['small', false, 'large', 'huge'] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'color': [] }, { 'background': [] }],

        // Paragraph formatting
        [{ 'align': [] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'indent': '-1'}, { 'indent': '+1' }],

        // Advanced features
        ['blockquote', 'code-block'],
        ['link', 'image', 'video'],
        ['table'],

        // Utilities
        ['clean', 'undo', 'redo']
      ],
      handlers: {
        'table': function() {
          const quill = this.quill
          const tableModule = quill.getModule('better-table')
          tableModule.insertTable(3, 3)
        },
        'undo': function() {
          this.quill.history.undo()
        },
        'redo': function() {
          this.quill.history.redo()
        }
      }
    },
    history: {
      delay: 1000,
      maxStack: 100,
      userOnly: true
    },
    imageResize: {
      parchment: typeof window !== 'undefined' ? window.Quill?.import('parchment') : null,
      modules: ['Resize', 'DisplaySize', 'Toolbar']
    },
    'better-table': {
      operationMenu: {
        items: {
          unmergeCells: {
            text: 'Another unmerge cells name'
          }
        }
      }
    },
    keyboard: {
      bindings: {
        // Custom keyboard shortcuts
        'bold': {
          key: 'B',
          ctrlKey: true,
          handler: function(range: any, context: any) {
            this.quill.format('bold', !context.format.bold)
          }
        },
        'italic': {
          key: 'I',
          ctrlKey: true,
          handler: function(range: any, context: any) {
            this.quill.format('italic', !context.format.italic)
          }
        },
        'underline': {
          key: 'U',
          ctrlKey: true,
          handler: function(range: any, context: any) {
            this.quill.format('underline', !context.format.underline)
          }
        }
      }
    }
  }

  // Calculate word count and reading time
  const calculateStats = useCallback((content: string) => {
    const text = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim()
    const words = text.split(' ').filter(word => word.length > 0).length
    const readingTimeMinutes = Math.ceil(words / 200) // Average reading speed: 200 words/minute

    setWordCount(words)
    setReadingTime(readingTimeMinutes)
  }, [])

  // Generate table of contents from headings
  const generateTableOfContents = useCallback((content: string) => {
    if (!enableTableOfContents) return

    const parser = new DOMParser()
    const doc = parser.parseFromString(content, 'text/html')
    const headings = doc.querySelectorAll('h1, h2, h3, h4, h5, h6')

    const toc = Array.from(headings).map((heading, index) => ({
      id: `heading-${index}`,
      level: parseInt(heading.tagName.charAt(1)),
      text: heading.textContent || '',
      anchor: heading.textContent?.toLowerCase().replace(/[^a-z0-9]+/g, '-') || `heading-${index}`
    }))

    setTableOfContents(toc)
    if (onTableOfContentsChange) {
      onTableOfContentsChange(toc)
    }
  }, [enableTableOfContents, onTableOfContentsChange])

  // Handle content changes
  const handleChange = useCallback((content: string) => {
    onChange(content)
    calculateStats(content)
    generateTableOfContents(content)
  }, [onChange, calculateStats, generateTableOfContents])

  // Initialize stats when component mounts
  useEffect(() => {
    if (value) {
      calculateStats(value)
      generateTableOfContents(value)
    }
  }, [value, calculateStats, generateTableOfContents])

  // Custom toolbar component
  const CustomToolbar = () => (
    <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
      <div className="flex items-center space-x-2">
        <button
          type="button"
          onClick={() => setIsPreviewMode(!isPreviewMode)}
          className="flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          {isPreviewMode ? <EyeOff className="w-4 h-4 mr-1" /> : <Eye className="w-4 h-4 mr-1" />}
          {isPreviewMode ? 'Edit' : 'Preview'}
        </button>
      </div>

      <div className="flex items-center space-x-4 text-sm text-gray-600">
        {showWordCount && (
          <div className="flex items-center">
            <Type className="w-4 h-4 mr-1" />
            <span>{wordCount} words</span>
          </div>
        )}
        {showReadingTime && (
          <div className="flex items-center">
            <FileText className="w-4 h-4 mr-1" />
            <span>{readingTime} min read</span>
          </div>
        )}
      </div>
    </div>
  )

  return (
    <Suspense fallback={
      <div className="bg-white border border-gray-300 rounded-lg flex items-center justify-center" style={{ height }}>
        <LoadingSpinner text="Loading professional editor..." />
      </div>
    }>
      <div className="bg-white border border-gray-300 rounded-lg overflow-hidden">
        <CustomToolbar />

        {isPreviewMode ? (
          <div
            className="p-6 prose prose-lg max-w-none"
            style={{ minHeight: height }}
            dangerouslySetInnerHTML={{ __html: value }}
          />
        ) : (
          <div className="relative">
            <ReactQuill
              ref={quillRef}
              theme="snow"
              value={value}
              onChange={handleChange}
              modules={modules}
              placeholder={placeholder}
              style={{ height }}
              className="professional-editor"
            />
          </div>
        )}

        {/* Table of Contents Sidebar */}
        {enableTableOfContents && tableOfContents.length > 0 && !isPreviewMode && (
          <div className="border-t border-gray-200 p-4 bg-gray-50">
            <h4 className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
              <Hash className="w-4 h-4 mr-1" />
              Table of Contents
            </h4>
            <ul className="space-y-1">
              {tableOfContents.map((item, index) => (
                <li key={index} style={{ marginLeft: `${(item.level - 1) * 12}px` }}>
                  <button
                    type="button"
                    className="text-sm text-gray-600 hover:text-primary-600 text-left"
                    onClick={() => {
                      const quill = quillRef.current?.getEditor()
                      if (quill) {
                        // Scroll to heading in editor
                        const headings = quill.root.querySelectorAll('h1, h2, h3, h4, h5, h6')
                        const targetHeading = headings[index]
                        if (targetHeading) {
                          targetHeading.scrollIntoView({ behavior: 'smooth' })
                        }
                      }
                    }}
                  >
                    {item.text}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      <style jsx global>{`
        .professional-editor .ql-toolbar {
          border: none !important;
          padding: 12px 16px !important;
          background: #f9fafb !important;
        }

        .professional-editor .ql-container {
          border: none !important;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
          font-size: 16px !important;
          line-height: 1.6 !important;
        }

        .professional-editor .ql-editor {
          padding: 24px !important;
          min-height: 400px !important;
        }

        .professional-editor .ql-editor h1 {
          font-size: 2.25rem !important;
          font-weight: 700 !important;
          margin: 1.5rem 0 1rem 0 !important;
          line-height: 1.2 !important;
        }

        .professional-editor .ql-editor h2 {
          font-size: 1.875rem !important;
          font-weight: 600 !important;
          margin: 1.25rem 0 0.75rem 0 !important;
          line-height: 1.3 !important;
        }

        .professional-editor .ql-editor h3 {
          font-size: 1.5rem !important;
          font-weight: 600 !important;
          margin: 1rem 0 0.5rem 0 !important;
          line-height: 1.4 !important;
        }

        .professional-editor .ql-editor blockquote {
          border-left: 4px solid #e5e7eb !important;
          padding-left: 1rem !important;
          margin: 1rem 0 !important;
          font-style: italic !important;
          color: #6b7280 !important;
        }

        .professional-editor .ql-editor pre {
          background: #f3f4f6 !important;
          border-radius: 0.375rem !important;
          padding: 1rem !important;
          margin: 1rem 0 !important;
          overflow-x: auto !important;
        }

        .professional-editor .ql-editor table {
          border-collapse: collapse !important;
          width: 100% !important;
          margin: 1rem 0 !important;
        }

        .professional-editor .ql-editor table td,
        .professional-editor .ql-editor table th {
          border: 1px solid #d1d5db !important;
          padding: 0.5rem !important;
        }

        .professional-editor .ql-editor table th {
          background: #f9fafb !important;
          font-weight: 600 !important;
        }

        .professional-editor .ql-editor img {
          max-width: 100% !important;
          height: auto !important;
          border-radius: 0.375rem !important;
          margin: 1rem 0 !important;
        }

        .professional-editor .ql-editor a {
          color: #3b82f6 !important;
          text-decoration: underline !important;
        }

        .professional-editor .ql-editor a:hover {
          color: #1d4ed8 !important;
        }
      `}</style>
    </Suspense>
  )
}