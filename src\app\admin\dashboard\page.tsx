'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase-client'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuthContext } from '@/contexts/AuthContext'
import { FileText, HelpCircle, Image as ImageIcon, Activity } from 'lucide-react'

interface DashboardStats {
  posts: number
  help_articles: number
  media_files: number
}

interface RecentActivity {
  id: string
  type: string
  title: string
  created_at: string
}

export default function DashboardPage() {
  const { user } = useAuthContext()
  const [stats, setStats] = useState<DashboardStats>({ posts: 0, help_articles: 0, media_files: 0 })
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    setLoading(true)
    try {
      const supabase = createClient()
      const [
        { count: posts },
        { count: help_articles },
        { count: media_files },
        { data: activity }
      ] = await Promise.all([
        supabase.from('posts').select('*', { count: 'exact', head: true }),
        supabase.from('help_articles').select('*', { count: 'exact', head: true }),
        supabase.from('media_files').select('*', { count: 'exact', head: true }),
        supabase.from('recent_activity').select('*').order('created_at', { ascending: false }).limit(5)
      ])
      
      setStats({ 
        posts: posts || 0, 
        help_articles: help_articles || 0, 
        media_files: media_files || 0 
      })
      setRecentActivity(activity || [])
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <div>Loading dashboard...</div>
  }

  return (
    <ProtectedRoute requiredRole={['admin', 'editor']}>
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Welcome, {user?.name || user?.email}!</h1>
          <p className="text-gray-600 mt-1">Here&apos;s a summary of your site.</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md border flex items-center">
            <div className="bg-blue-100 p-3 rounded-full mr-4">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Posts</p>
              <p className="text-2xl font-bold text-gray-900">{stats.posts}</p>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-md border flex items-center">
            <div className="bg-green-100 p-3 rounded-full mr-4">
              <HelpCircle className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Help Articles</p>
              <p className="text-2xl font-bold text-gray-900">{stats.help_articles}</p>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-md border flex items-center">
            <div className="bg-purple-100 p-3 rounded-full mr-4">
              <ImageIcon className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Media Files</p>
              <p className="text-2xl font-bold text-gray-900">{stats.media_files}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
            <Activity className="w-5 h-5 mr-3" />
            Recent Activity
          </h2>
          <ul className="divide-y divide-gray-200">
            {recentActivity.map(item => (
              <li key={item.id} className="py-3">
                <p className="text-sm text-gray-800">{item.title}</p>
                <p className="text-xs text-gray-500">{item.type} - {new Date(item.created_at).toLocaleString()}</p>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </ProtectedRoute>
  )
}