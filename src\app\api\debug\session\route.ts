import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    const { data: { session }, error } = await supabase.auth.getSession()

    return NextResponse.json({
      hasSession: !!session,
      userEmail: session?.user?.email,
      userId: session?.user?.id,
      error: error?.message,
      cookies: request.cookies.getAll().map(c => ({ name: c.name, value: c.value.substring(0, 20) + '...' }))
    })
  } catch (err) {
    console.error('Debug session error:', err)
    return NextResponse.json({ error: 'Failed to check session' }, { status: 500 })
  }
}
