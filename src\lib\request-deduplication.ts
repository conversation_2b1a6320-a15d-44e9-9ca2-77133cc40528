// Request deduplication utility to prevent duplicate API calls

interface PendingRequest<T> {
  promise: Promise<T>
  timestamp: number
}

class RequestDeduplicator {
  private pendingRequests = new Map<string, PendingRequest<any>>()
  private readonly CACHE_DURATION = 5000 // 5 seconds

  // Generate a unique key for the request
  private generateKey(url: string, options?: RequestInit): string {
    const method = options?.method || 'GET'
    const body = options?.body ? JSON.stringify(options.body) : ''
    const headers = options?.headers ? JSON.stringify(options.headers) : ''
    
    return `${method}:${url}:${body}:${headers}`
  }

  // Clean up expired requests
  private cleanup(): void {
    const now = Date.now()
    for (const [key, request] of this.pendingRequests.entries()) {
      if (now - request.timestamp > this.CACHE_DURATION) {
        this.pendingRequests.delete(key)
      }
    }
  }

  // Deduplicate fetch requests
  async fetch<T = any>(url: string, options?: RequestInit): Promise<T> {
    this.cleanup()
    
    const key = this.generateKey(url, options)
    const existing = this.pendingRequests.get(key)

    // Return existing promise if request is already in flight
    if (existing) {
      console.log(`🔄 Deduplicating request: ${key}`)
      return existing.promise
    }

    // Create new request
    const promise = this.executeRequest<T>(url, options)
    
    // Store the promise
    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now(),
    })

    // Clean up after request completes
    promise
      .finally(() => {
        this.pendingRequests.delete(key)
      })

    return promise
  }

  private async executeRequest<T>(url: string, options?: RequestInit): Promise<T> {
    const response = await fetch(url, options)
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
    }

    return response.json()
  }

  // Clear all pending requests (useful for cleanup)
  clear(): void {
    this.pendingRequests.clear()
  }

  // Get number of pending requests (for debugging)
  getPendingCount(): number {
    this.cleanup()
    return this.pendingRequests.size
  }
}

// Singleton instance
export const requestDeduplicator = new RequestDeduplicator()

// Convenience function for deduplicating fetch requests
export function deduplicatedFetch<T = any>(url: string, options?: RequestInit): Promise<T> {
  return requestDeduplicator.fetch<T>(url, options)
}

// React hook for deduplicating requests
export function useDeduplicatedFetch() {
  return deduplicatedFetch
}

// Batch request utility
export class BatchRequestManager {
  private batches = new Map<string, {
    requests: Array<{
      resolve: (value: any) => void
      reject: (error: any) => void
      id: string
    }>
    timer: NodeJS.Timeout
  }>()

  private readonly BATCH_DELAY = 50 // 50ms delay to collect requests

  // Add a request to a batch
  addToBatch<T>(batchKey: string, requestId: string, executor: () => Promise<T[]>): Promise<T> {
    return new Promise((resolve, reject) => {
      let batch = this.batches.get(batchKey)

      if (!batch) {
        batch = {
          requests: [],
          timer: setTimeout(() => this.executeBatch(batchKey, executor), this.BATCH_DELAY)
        }
        this.batches.set(batchKey, batch)
      }

      batch.requests.push({ resolve, reject, id: requestId })
    })
  }

  private async executeBatch<T>(batchKey: string, executor: () => Promise<T[]>): Promise<void> {
    const batch = this.batches.get(batchKey)
    if (!batch) return

    this.batches.delete(batchKey)

    try {
      const results = await executor()
      
      // Resolve each request with its corresponding result
      batch.requests.forEach((request, index) => {
        if (results[index] !== undefined) {
          request.resolve(results[index])
        } else {
          request.reject(new Error(`No result for request ${request.id}`))
        }
      })
    } catch (error) {
      // Reject all requests in the batch
      batch.requests.forEach(request => {
        request.reject(error)
      })
    }
  }

  // Clear all batches
  clear(): void {
    for (const batch of this.batches.values()) {
      clearTimeout(batch.timer)
    }
    this.batches.clear()
  }
}

// Singleton batch manager
export const batchRequestManager = new BatchRequestManager()

// Utility for batching similar requests
export function batchRequest<T>(
  batchKey: string,
  requestId: string,
  executor: () => Promise<T[]>
): Promise<T> {
  return batchRequestManager.addToBatch(batchKey, requestId, executor)
}
