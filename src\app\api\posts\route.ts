import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { supabaseAdmin } from '@/lib/supabase'
import { logger } from '@/lib/logger'
import { Post } from '@/types'

const querySchema = z.object({
  searchTerm: z.string().optional(),
  category: z.string().optional(),
  status: z.string().optional(),
  page: z.coerce.number().int().min(1).optional().default(1),
  limit: z.coerce.number().int().min(1).max(100).optional().default(10),
})

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      logger.warn('Unauthorized access attempt to posts API', { ip: request.ip })
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: userRole, error: roleError } = await supabase.rpc('get_user_role', { user_id: session.user.id })

    if (roleError) {
      logger.error('Error fetching user role', roleError)
      return NextResponse.json({ error: 'Failed to fetch user role' }, { status: 500 })
    }

    if (userRole !== 'admin' && userRole !== 'editor') {
      logger.warn('Forbidden access attempt to posts API', { userId: session.user.id, userRole })
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    const validation = querySchema.safeParse(queryParams)

    if (!validation.success) {
      logger.warn('Invalid query parameters for posts API', { errors: validation.error.flatten() })
      return NextResponse.json({ error: 'Invalid query parameters', details: validation.error.flatten() }, { status: 400 })
    }

    const { searchTerm, category, status, page, limit } = validation.data
    const offset = (page - 1) * limit

    let query = supabase
      .from('posts')
      .select(`
        *,
        author:users(name, email),
        category:categories(name, slug)
      `, { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (searchTerm) {
      query = query.ilike('title', `%${searchTerm}%`)
    }

    if (category !== 'all') {
      query = query.eq('category_id', category)
    }

    if (status !== 'all') {
      query = query.eq('status', status)
    }

    const { data, error, count } = await query

    if (error) {
      logger.error('Error fetching posts from Supabase', error, { query: query.toString() })
      return NextResponse.json({ error: 'Failed to fetch posts', details: error.message }, { status: 500 })
    }

    logger.info('Successfully fetched posts', { page, limit, searchTerm, category, status, count })

    return NextResponse.json({
      posts: data,
      count: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    })
  } catch (err) {
    const error = err instanceof Error ? err : new Error('An unexpected error occurred')
    logger.error('An unexpected error occurred in posts API', error)
    return NextResponse.json({ error: 'Server error', details: error.message }, { status: 500 })
  }
}