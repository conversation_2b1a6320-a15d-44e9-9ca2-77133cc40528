-- Performance Optimization Migration Script
-- Run this on your live database to add performance improvements
-- This script is safe to run multiple times (idempotent)

-- Enable pg_trgm extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Add performance indexes for text search (only if they don't exist)
DO $$
BEGIN
    -- Trigram indexes for faster ILIKE searches
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_posts_title_trgm') THEN
        CREATE INDEX idx_posts_title_trgm ON public.posts USING gin(title gin_trgm_ops);
        RAISE NOTICE 'Created index: idx_posts_title_trgm';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_posts_content_trgm') THEN
        CREATE INDEX idx_posts_content_trgm ON public.posts USING gin(content gin_trgm_ops);
        RAISE NOTICE 'Created index: idx_posts_content_trgm';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_help_articles_title_trgm') THEN
        CREATE INDEX idx_help_articles_title_trgm ON public.help_articles USING gin(title gin_trgm_ops);
        RAISE NOTICE 'Created index: idx_help_articles_title_trgm';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_help_articles_content_trgm') THEN
        CREATE INDEX idx_help_articles_content_trgm ON public.help_articles USING gin(content gin_trgm_ops);
        RAISE NOTICE 'Created index: idx_help_articles_content_trgm';
    END IF;

    -- Composite indexes for common query patterns
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_posts_status_created_at') THEN
        CREATE INDEX idx_posts_status_created_at ON public.posts(status, created_at DESC);
        RAISE NOTICE 'Created index: idx_posts_status_created_at';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_posts_category_status_created_at') THEN
        CREATE INDEX idx_posts_category_status_created_at ON public.posts(category_id, status, created_at DESC);
        RAISE NOTICE 'Created index: idx_posts_category_status_created_at';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_help_articles_status_order_index') THEN
        CREATE INDEX idx_help_articles_status_order_index ON public.help_articles(status, order_index);
        RAISE NOTICE 'Created index: idx_help_articles_status_order_index';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_help_articles_category_status') THEN
        CREATE INDEX idx_help_articles_category_status ON public.help_articles(category_id, status);
        RAISE NOTICE 'Created index: idx_help_articles_category_status';
    END IF;

    -- Pagination performance indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_posts_created_at_desc') THEN
        CREATE INDEX idx_posts_created_at_desc ON public.posts(created_at DESC);
        RAISE NOTICE 'Created index: idx_posts_created_at_desc';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_help_articles_created_at_desc') THEN
        CREATE INDEX idx_help_articles_created_at_desc ON public.help_articles(created_at DESC);
        RAISE NOTICE 'Created index: idx_help_articles_created_at_desc';
    END IF;

    -- Additional performance indexes for media files
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_media_files_created_at_desc') THEN
        CREATE INDEX idx_media_files_created_at_desc ON public.media_files(created_at DESC);
        RAISE NOTICE 'Created index: idx_media_files_created_at_desc';
    END IF;

    -- Index for user lookups
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_role') THEN
        CREATE INDEX idx_users_role ON public.users(role);
        RAISE NOTICE 'Created index: idx_users_role';
    END IF;

END $$;

-- Add new columns for enhanced blogging features (only if they don't exist)
DO $$
BEGIN
    -- Add SEO fields to posts table
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'posts' AND column_name = 'meta_title') THEN
        ALTER TABLE public.posts ADD COLUMN meta_title TEXT;
        RAISE NOTICE 'Added column: posts.meta_title';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'posts' AND column_name = 'meta_description') THEN
        ALTER TABLE public.posts ADD COLUMN meta_description TEXT;
        RAISE NOTICE 'Added column: posts.meta_description';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'posts' AND column_name = 'canonical_url') THEN
        ALTER TABLE public.posts ADD COLUMN canonical_url TEXT;
        RAISE NOTICE 'Added column: posts.canonical_url';
    END IF;

    -- Add reading time estimation
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'posts' AND column_name = 'reading_time') THEN
        ALTER TABLE public.posts ADD COLUMN reading_time INTEGER DEFAULT 0;
        RAISE NOTICE 'Added column: posts.reading_time';
    END IF;

    -- Add social media fields
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'posts' AND column_name = 'social_image') THEN
        ALTER TABLE public.posts ADD COLUMN social_image TEXT;
        RAISE NOTICE 'Added column: posts.social_image';
    END IF;

    -- Add content structure fields
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'posts' AND column_name = 'table_of_contents') THEN
        ALTER TABLE public.posts ADD COLUMN table_of_contents JSONB;
        RAISE NOTICE 'Added column: posts.table_of_contents';
    END IF;

    -- Add scheduling fields
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'posts' AND column_name = 'scheduled_at') THEN
        ALTER TABLE public.posts ADD COLUMN scheduled_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added column: posts.scheduled_at';
    END IF;

    -- Add view count for analytics
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'posts' AND column_name = 'view_count') THEN
        ALTER TABLE public.posts ADD COLUMN view_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added column: posts.view_count';
    END IF;

    -- Add same fields to help_articles
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'help_articles' AND column_name = 'meta_title') THEN
        ALTER TABLE public.help_articles ADD COLUMN meta_title TEXT;
        RAISE NOTICE 'Added column: help_articles.meta_title';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'help_articles' AND column_name = 'meta_description') THEN
        ALTER TABLE public.help_articles ADD COLUMN meta_description TEXT;
        RAISE NOTICE 'Added column: help_articles.meta_description';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'help_articles' AND column_name = 'reading_time') THEN
        ALTER TABLE public.help_articles ADD COLUMN reading_time INTEGER DEFAULT 0;
        RAISE NOTICE 'Added column: help_articles.reading_time';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'help_articles' AND column_name = 'table_of_contents') THEN
        ALTER TABLE public.help_articles ADD COLUMN table_of_contents JSONB;
        RAISE NOTICE 'Added column: help_articles.table_of_contents';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'help_articles' AND column_name = 'view_count') THEN
        ALTER TABLE public.help_articles ADD COLUMN view_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added column: help_articles.view_count';
    END IF;

END $$;

-- Create function to calculate reading time
CREATE OR REPLACE FUNCTION calculate_reading_time(content TEXT)
RETURNS INTEGER AS $$
DECLARE
    word_count INTEGER;
    reading_time INTEGER;
BEGIN
    -- Remove HTML tags and count words
    word_count := array_length(string_to_array(regexp_replace(content, '<[^>]*>', ' ', 'g'), ' '), 1);
    -- Average reading speed is 200 words per minute
    reading_time := CEIL(word_count::FLOAT / 200);
    RETURN GREATEST(reading_time, 1); -- Minimum 1 minute
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update reading time
CREATE OR REPLACE FUNCTION update_reading_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.reading_time := calculate_reading_time(NEW.content);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for automatic reading time calculation
DROP TRIGGER IF EXISTS posts_reading_time_trigger ON public.posts;
CREATE TRIGGER posts_reading_time_trigger
    BEFORE INSERT OR UPDATE OF content ON public.posts
    FOR EACH ROW EXECUTE FUNCTION update_reading_time();

DROP TRIGGER IF EXISTS help_articles_reading_time_trigger ON public.help_articles;
CREATE TRIGGER help_articles_reading_time_trigger
    BEFORE INSERT OR UPDATE OF content ON public.help_articles
    FOR EACH ROW EXECUTE FUNCTION update_reading_time();

-- Update existing records with reading time
UPDATE public.posts SET reading_time = calculate_reading_time(content) WHERE reading_time = 0 OR reading_time IS NULL;
UPDATE public.help_articles SET reading_time = calculate_reading_time(content) WHERE reading_time = 0 OR reading_time IS NULL;

-- Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_posts_scheduled_at ON public.posts(scheduled_at) WHERE scheduled_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_posts_view_count ON public.posts(view_count DESC);
CREATE INDEX IF NOT EXISTS idx_help_articles_view_count ON public.help_articles(view_count DESC);

RAISE NOTICE 'Performance optimization migration completed successfully!';
