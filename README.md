# Eria CMS - Modern Content Management System

A powerful, modern Content Management System built with Next.js 15, TypeScript, Supabase, and Tailwind CSS. Designed for managing blog posts, help articles, and downloadable files with advanced features like content versioning, search functionality, and role-based access control.

## 🚀 Features

### ✅ Completed Features
- **Modern Tech Stack**: Next.js 15, TypeScript, Supabase, Tailwind CSS
- **Authentication System**: Complete user authentication with role-based access (Admin, Editor, Viewer)
- **Responsive Admin Dashboard**: Modern, mobile-friendly admin interface
- **Database Schema**: Comprehensive PostgreSQL schema with relationships and indexing
- **Security**: Row Level Security (RLS) policies and protected routes

### 🚧 In Development
- **Blog Post Management**: Create, edit, delete posts with categories and tags
- **Help Articles**: Comprehensive help documentation system
- **File Management**: Upload, organize, and manage media files
- **Search Functionality**: Full-text search across all content
- **Content Versioning**: Track changes and rollback capabilities
- **Analytics Dashboard**: Content performance and user analytics

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account
- Git

## 🛠️ Installation & Setup

### 1. Clone the Repository
```bash
git clone <your-repo-url>
cd eria-cms
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Set Up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Go to Settings > Database and run the SQL from `database/schema.sql`

### 4. Environment Configuration

Copy the example environment file:
```bash
cp .env.local.example .env.local
```

Update `.env.local` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=Eria CMS
```

### 5. Database Setup

1. In your Supabase dashboard, go to the SQL Editor
2. Copy and paste the contents of `database/schema.sql`
3. Run the SQL to create all tables, indexes, and policies

### 6. Configure Supabase Storage

1. Go to Storage in your Supabase dashboard
2. Create a new bucket called `uploads`
3. Set the bucket to public if you want direct file access

### 7. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 🏗️ Project Structure

```
eria-cms/
├── src/
│   ├── app/                    # Next.js 13+ app directory
│   │   ├── admin/             # Admin dashboard pages
│   │   ├── login/             # Authentication pages
│   │   ├── signup/            
│   │   └── unauthorized/      
│   ├── components/            # Reusable UI components
│   ├── contexts/              # React contexts (Auth, etc.)
│   ├── hooks/                 # Custom React hooks
│   ├── lib/                   # Utilities and configurations
│   └── types/                 # TypeScript type definitions
├── database/                  # Database schema and migrations
├── public/                    # Static assets
└── docs/                      # Documentation
```

## 🔐 User Roles & Permissions

### Admin
- Full access to all features
- User management
- System settings
- All content operations

### Editor
- Create, edit, delete own content
- Manage categories and tags
- Upload and manage media files
- View analytics

### Viewer
- Read-only access to published content
- Cannot create or edit content

## 🎨 UI Components

The system uses a custom design system built with Tailwind CSS:

- **Cards**: `.card` - Standard content containers
- **Buttons**: `.btn-primary`, `.btn-secondary` - Consistent button styles
- **Forms**: `.input-field`, `.textarea-field` - Form input styling
- **Colors**: Primary blue theme with semantic color variants

## 📊 Database Schema

### Core Tables
- `users` - User accounts with role-based access
- `categories` - Content categorization
- `tags` - Content tagging system
- `posts` - Blog posts with versioning
- `help_articles` - Help documentation
- `media_files` - File management with metadata
- `content_versions` - Version history tracking

### Key Features
- **Full-text search** with PostgreSQL GIN indexes
- **Row Level Security** for data protection
- **Automatic timestamps** with triggers
- **Referential integrity** with foreign keys

## 🔍 API Routes

The system uses Supabase for backend operations, but you can extend with custom API routes:

```
/api/auth/          # Authentication endpoints
/api/posts/         # Blog post operations
/api/help/          # Help article operations
/api/media/         # File upload and management
/api/search/        # Search functionality
```

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms
The application can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join community discussions in GitHub Discussions

## 🔄 Roadmap

### Phase 1 (Current)
- ✅ Authentication & Authorization
- ✅ Admin Dashboard
- 🚧 Content Management Core

### Phase 2
- Blog Post Management
- Help Articles System
- File Management

### Phase 3
- Search & Analytics
- Content Versioning
- Advanced Features

### Phase 4
- API Documentation
- Plugin System
- Multi-language Support

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - React framework
- [Supabase](https://supabase.com/) - Backend as a Service
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [Lucide React](https://lucide.dev/) - Beautiful icons
- [React Quill](https://github.com/zenoamaro/react-quill) - Rich text editor

---

Built with ❤️ for modern content management needs.