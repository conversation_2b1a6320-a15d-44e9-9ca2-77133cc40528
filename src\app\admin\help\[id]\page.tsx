'use client'

import { useState, useEffect, useCallback } from 'react'
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeft, 
  Edit, 
  Eye, 
  Calendar, 
  User, 
  Tag as TagIcon,
  Folder,
  HelpCircle,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { createClient } from '@/lib/supabase-client'
import { HelpArticle } from '@/types'
import ProtectedRoute from '@/components/ProtectedRoute'
import { formatDate } from '@/lib/utils'

export default function HelpArticleViewPage() {
  const params = useParams()
  const router = useRouter()
  const supabase = createClient()
  const searchParams = useSearchParams()
  const { id } = params
  
  const [article, setArticle] = useState<HelpArticle | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const created = searchParams.get('created')
  const updated = searchParams.get('updated')

  const fetchArticle = useCallback(async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('help_articles')
        .select(`
          *,
          author:users(name, email),
          category:categories(name, slug),
          tags:help_article_tags(tag:tags(name, slug, color))
        `)
        .eq('id', id)
        .single()

      if (error) throw error
      setArticle(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Help article not found')
    } finally {
      setLoading(false)
    }
  }, [id, supabase])

  useEffect(() => {
    fetchArticle()
  }, [fetchArticle])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800'
      case 'draft': return 'bg-yellow-100 text-yellow-800'
      case 'archived': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <HelpCircle className="w-8 h-8 text-gray-400 mx-auto mb-4 animate-spin" />
          <p className="text-gray-500">Loading help article...</p>
        </div>
      </div>
    )
  }

  if (error || !article) {
    return (
      <div className="text-center py-12">
        <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Help article not found</h3>
        <p className="text-gray-500 mb-4">{error || 'The help article you are looking for does not exist.'}</p>
        <Link
          href="/admin/help"
          className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Help Articles
        </Link>
      </div>
    )
  }

  return (
    <ProtectedRoute requiredRole={['admin', 'editor']}>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/admin/help" className="flex items-center text-gray-600 hover:text-gray-900 mr-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Help Articles
            </Link>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href={`/help/${article.slug}`}
              className="flex items-center px-3 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Eye className="w-4 h-4 mr-2" />
              View Live
            </Link>
            <Link
              href={`/admin/help/${article.id}/edit`}
              className="flex items-center px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Link>
          </div>
        </div>

        {/* Success Messages */}
        {created && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg flex items-start">
            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-green-800">Help article created successfully!</p>
            </div>
          </div>
        )}

        {updated && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-start">
            <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-blue-800">Help article updated successfully!</p>
            </div>
          </div>
        )}

        {/* Article Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Article Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">{article.title}</h1>
                {article.excerpt && (
                  <p className="text-gray-600 mb-4">{article.excerpt}</p>
                )}
                
                {/* Meta Information */}
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <User className="w-4 h-4 mr-1" />
                    {article.author?.name || article.author?.email || 'Unknown'}
                  </div>
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {formatDate(article.created_at)}
                  </div>
                  {article.category && (
                    <div className="flex items-center">
                      <Folder className="w-4 h-4 mr-1" />
                      {article.category.name}
                    </div>
                  )}
                  <div className="flex items-center">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(article.status)}`}>
                      {article.status}
                    </span>
                  </div>
                  {article.order_index !== undefined && (
                    <div className="flex items-center">
                      <span className="text-xs">Order: {article.order_index}</span>
                    </div>
                  )}
                </div>

                {/* Tags */}
                {article.tags && article.tags.length > 0 && (
                  <div className="flex items-center mt-4">
                    <TagIcon className="w-4 h-4 text-gray-400 mr-2" />
                    <div className="flex flex-wrap gap-2">
                      {article.tags.map((tagRelation: any) => (
                        <span
                          key={tagRelation.tag.slug}
                          className="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                          style={{
                            backgroundColor: tagRelation.tag.color ? `${tagRelation.tag.color}20` : '#f3f4f6',
                            color: tagRelation.tag.color || '#6b7280'
                          }}
                        >
                          {tagRelation.tag.name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Article Content */}
          <div className="px-6 py-6">
            <div 
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: article.content }}
            />
          </div>

          {/* Article Footer */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm text-gray-500">
              <div>
                Version {article.version || 1}
              </div>
              <div>
                Last updated: {formatDate(article.updated_at)}
              </div>
            </div>
          </div>
        </div>

        {/* Article Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="bg-blue-100 p-3 rounded-full mr-4">
                <HelpCircle className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <p className="text-lg font-semibold text-gray-900 capitalize">{article.status}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="bg-green-100 p-3 rounded-full mr-4">
                <Calendar className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Created</p>
                <p className="text-lg font-semibold text-gray-900">{formatDate(article.created_at)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="bg-purple-100 p-3 rounded-full mr-4">
                <Edit className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Last Updated</p>
                <p className="text-lg font-semibold text-gray-900">{formatDate(article.updated_at)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
