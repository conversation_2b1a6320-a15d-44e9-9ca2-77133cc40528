# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://buzodtofrksaeuxdvrms.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ1em9kdG9mcmtzYWV1eGR2cm1zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM1OTMwMTIsImV4cCI6MjA2OTE2OTAxMn0.9jOYdlxJzw6oCeRj_QgY-5kXViBNgaiprPTsE_3oRMg
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ1em9kdG9mcmtzYWV1eGR2cm1zIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzU5MzAxMiwiZXhwIjoyMDY5MTY5MDEyfQ.99PFLsj4yl3Se09flSaSpvw8ZPfE4BXP2C1FKep6Nsg

# Application Configuration
# IMPORTANT: Update this to your production domain for proper email links
# For development: http://localhost:3000
# For production: https://yourdomain.com
NEXT_PUBLIC_APP_URL=https://cms.eriasoftware.com
NEXT_PUBLIC_APP_NAME=Eria CMS
APP_URL=https://cms.eriasoftware.com

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# Email Configuration (Optional)
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASS=
