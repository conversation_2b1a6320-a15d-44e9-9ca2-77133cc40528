import Link from 'next/link'
import { ShieldX, ArrowLeft } from 'lucide-react'

export default function UnauthorizedPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-white flex items-center justify-center p-4">
      <div className="w-full max-w-md text-center">
        <div className="card">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <ShieldX className="w-8 h-8 text-red-600" />
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Access Denied
          </h1>
          
          <p className="text-gray-600 mb-6">
            You don&apos;t have permission to access this page. Please contact an administrator if you believe this is an error.
          </p>
          
          <div className="space-y-3">
            <Link
              href="/"
              className="btn-primary w-full inline-flex items-center justify-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Go to Home
            </Link>
            
            <Link
              href="/login"
              className="btn-secondary w-full inline-flex items-center justify-center"
            >
              Sign In with Different Account
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}