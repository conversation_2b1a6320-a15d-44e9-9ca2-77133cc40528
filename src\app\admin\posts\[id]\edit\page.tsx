'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { 
  Save, 
  ArrowLeft, 
  Tag, 
  Folder,
  FileText,
  AlertCircle,
  Trash2
} from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { Category, Tag as TagType, PostFormData, Post } from '@/types'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuthContext } from '@/contexts/AuthContext'
import { generateSlug } from '@/lib/utils'
import RichTextEditor from '@/components/RichTextEditor'

export default function EditPostPage() {
  const router = useRouter()
  const params = useParams()
  const { id } = params
  const { user } = useAuthContext()
  
  const [post, setPost] = useState<Post | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const [tags, setTags] = useState<TagType[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState<PostFormData>({
    title: '',
    content: '',
    excerpt: '',
    featured_image: '',
    status: 'draft',
    category_id: '',
    tag_ids: []
  })



  const fetchPostData = useCallback(async () => {
    if (!id) return
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('posts')
        .select('*, post_tags(tag_id)')
        .eq('id', id)
        .single()

      if (error) throw error

      setPost(data)
      setFormData({
        title: data.title,
        content: data.content,
        excerpt: data.excerpt || '',
        featured_image: data.featured_image || '',
        status: data.status,
        category_id: data.category_id || '',
        tag_ids: data.post_tags.map((pt: any) => pt.tag_id)
      })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch post data')
    } finally {
      setLoading(false)
    }
  }, [id])

  const fetchCategories = useCallback(async () => {
    try {
      const { data, error } = await supabase.from('categories').select('*').order('name')
      if (error) throw error
      setCategories(data || [])
    } catch (err) {
      console.error('Failed to fetch categories:', err)
    }
  }, [])

  const fetchTags = useCallback(async () => {
    try {
      const { data, error } = await supabase.from('tags').select('*').order('name')
      if (error) throw error
      setTags(data || [])
    } catch (err) {
      console.error('Failed to fetch tags:', err)
    }
  }, [])

  useEffect(() => {
    fetchPostData()
    fetchCategories()
    fetchTags()
  }, [fetchPostData, fetchCategories, fetchTags])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !post) return

    // Validate required fields
    if (!formData.title.trim()) {
      setError('Title is required')
      return
    }
    if (!formData.content.trim()) {
      setError('Content is required')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const slug = generateSlug(formData.title)

      // Check if slug already exists for a DIFFERENT post
      const { data: existingPost } = await supabase
        .from('posts')
        .select('id')
        .eq('slug', slug)
        .not('id', 'eq', post.id)
        .single()

      if (existingPost) {
        throw new Error('A post with this title already exists. Please choose a different title.')
      }

      // Generate excerpt if not provided
      const excerpt = formData.excerpt?.trim() ||
        formData.content.replace(/<[^>]*>/g, '').substring(0, 160) + '...'

      // Increment version for tracking changes
      const newVersion = (post.version || 1) + 1

      const postData = {
        title: formData.title.trim(),
        slug,
        content: formData.content,
        excerpt: excerpt,
        featured_image: formData.featured_image?.trim() || null,
        status: formData.status,
        category_id: formData.category_id || null,
        published_at: formData.status === 'published' && !post.published_at ? new Date().toISOString() : post.published_at,
        updated_at: new Date().toISOString(),
        version: newVersion
      }

      const { error: postError } = await supabase
        .from('posts')
        .update(postData)
        .eq('id', post.id)

      if (postError) throw postError

      // Update tags - delete existing and add new ones
      const { error: deleteTagError } = await supabase
        .from('post_tags')
        .delete()
        .eq('post_id', post.id)

      if (deleteTagError) throw deleteTagError

      if (formData.tag_ids && formData.tag_ids.length > 0) {
        const tagRelations = formData.tag_ids.map(tagId => ({
          post_id: post.id,
          tag_id: tagId
        }))
        const { error: tagError } = await supabase
          .from('post_tags')
          .insert(tagRelations)
        if (tagError) throw tagError
      }

      router.push(`/admin/posts/${post.id}?updated=true`)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update post')
    } finally {
      setLoading(false)
    }
  }

  const deletePost = async () => {
    if (!post || !confirm('Are you sure you want to delete this post? This action cannot be undone.')) return

    setLoading(true)
    setError(null)
    try {
      const { error } = await supabase.from('posts').delete().eq('id', post.id)
      if (error) throw error
      router.push('/admin/posts')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete post')
      setLoading(false)
    }
  }

  const handleTagToggle = (tagId: string) => {
    setFormData(prev => ({
      ...prev,
      tag_ids: prev.tag_ids?.includes(tagId)
        ? prev.tag_ids.filter(id => id !== tagId)
        : [...(prev.tag_ids || []), tagId]
    }))
  }

  if (!post && loading) {
    return <div>Loading...</div>
  }

  if (!post) {
    return <div>Post not found.</div>
  }

  return (
    <ProtectedRoute requiredRole={['admin', 'editor']}>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button onClick={() => router.back()} className="flex items-center text-gray-600 hover:text-gray-900">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Blog Post</h1>
              <p className="text-gray-600 mt-1">Update the content and settings of your post</p>
            </div>
          </div>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
            <AlertCircle className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-red-800">{error}</p>
              <button onClick={() => setError(null)} className="mt-2 text-sm text-red-600 hover:text-red-800">
                Dismiss
              </button>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">Post Title *</label>
                <input
                  type="text"
                  id="title"
                  required
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">Post Content *</label>
                <RichTextEditor
                  value={formData.content}
                  onChange={(value) => setFormData(prev => ({ ...prev, content: value }))}
                />
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 mb-2">Excerpt</label>
                <textarea
                  id="excerpt"
                  rows={3}
                  value={formData.excerpt}
                  onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <label htmlFor="featured_image" className="block text-sm font-medium text-gray-700 mb-2">Featured Image URL</label>
                <input
                  type="url"
                  id="featured_image"
                  value={formData.featured_image}
                  onChange={(e) => setFormData(prev => ({ ...prev, featured_image: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>

            <div className="space-y-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center"><FileText className="w-5 h-5 mr-2" />Publish Settings</h3>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  id="status"
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center"><Folder className="w-5 h-5 mr-2" />Category</h3>
                <select
                  value={formData.category_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, category_id: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center"><Tag className="w-5 h-5 mr-2" />Tags</h3>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {tags.map(tag => (
                    <label key={tag.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.tag_ids?.includes(tag.id) || false}
                        onChange={() => handleTagToggle(tag.id)}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{tag.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div className="space-y-3">
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50"
                  >
                    {loading ? 'Saving...' : 'Save Changes'}
                  </button>
                  <button
                    type="button"
                    onClick={deletePost}
                    disabled={loading}
                    className="w-full flex items-center justify-center px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 disabled:opacity-50"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Post
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </ProtectedRoute>
  )
}