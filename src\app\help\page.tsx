'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { HelpArticle } from '@/types'
import { ArrowRight, BookOpen, Home, ChevronRight, Search, HelpCircle, FileText } from 'lucide-react'
import { Card, CardBody } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

export default function HelpCenterPage() {
  const [articles, setArticles] = useState<HelpArticle[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [searchTerm, setSearchTerm] = useState('')

  const fetchArticles = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/public/help?page=${page}&limit=12`)
      if (!response.ok) {
        throw new Error('Failed to fetch help articles')
      }
      const data = await response.json()
      setArticles(data.articles)
      setTotalPages(data.totalPages)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setLoading(false)
    }
  }, [page])

  useEffect(() => {
    fetchArticles()
  }, [fetchArticles])

  if (loading && articles.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-neutral-600">Loading articles...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <p className="text-error-600 text-lg">{error}</p>
          <Button className="mt-4" onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-neutral-50 min-h-screen">
      {/* Navigation Breadcrumb */}
      <div className="bg-white border-b border-neutral-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-neutral-500 hover:text-neutral-700 transition-colors">
              <Home className="w-4 h-4" />
            </Link>
            <ChevronRight className="w-4 h-4 text-neutral-400" />
            <span className="text-neutral-900 font-medium">Help Center</span>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-16">
        {/* Header */}
        <div className="text-center mb-12 sm:mb-16">
          <Badge variant="primary" className="mb-4 sm:mb-6">
            <HelpCircle className="w-3 h-3 mr-1" />
            Documentation
          </Badge>
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-900 mb-4 sm:mb-6 px-4">
            Help <span className="text-gradient">Center</span>
          </h1>
          <p className="text-lg sm:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed mb-8 sm:mb-10 px-4">
            Find answers, tutorials, and guides to help you get the most out of Eria CMS.
            Everything you need to know in one place.
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto px-4">
            <Input
              placeholder="Search help articles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="w-5 h-5" />}
              className="text-base sm:text-lg py-3 sm:py-4"
            />
          </div>
        </div>

        {/* Help Articles Grid */}
        <div className="grid gap-6 sm:gap-8 md:grid-cols-2 lg:grid-cols-3">
          {articles
            .filter(article =>
              searchTerm === '' ||
              article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
              article.excerpt?.toLowerCase().includes(searchTerm.toLowerCase())
            )
            .map((article) => (
            <Link href={`/help/${article.slug}`} key={article.id} className="group">
              <Card hover className="h-full">
                <CardBody className="flex flex-col h-full">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-accent-500 to-accent-600 rounded-xl flex items-center justify-center">
                        <FileText className="w-6 h-6 text-white" />
                      </div>
                      {article.category && (
                        <Badge variant="success" size="sm">
                          {article.category.name}
                        </Badge>
                      )}
                    </div>

                    <h3 className="text-xl font-semibold text-neutral-900 mb-3 group-hover:text-primary-600 transition-colors line-clamp-2">
                      {article.title}
                    </h3>

                    {article.excerpt && (
                      <p className="text-neutral-600 line-clamp-3 leading-relaxed">
                        {article.excerpt}
                      </p>
                    )}
                  </div>

                  <div className="mt-6 flex items-center justify-between">
                    <span className="text-sm font-medium text-primary-600 group-hover:text-primary-700 transition-colors">
                      Read article
                    </span>
                    <ArrowRight className="w-4 h-4 text-neutral-400 group-hover:text-primary-600 group-hover:translate-x-1 transition-all duration-200" />
                  </div>
                </CardBody>
              </Card>
            </Link>
          ))}
        </div>

        {articles.length === 0 && !loading && (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <HelpCircle className="w-8 h-8 text-neutral-400" />
            </div>
            <h3 className="text-xl font-semibold text-neutral-900 mb-2">No articles found</h3>
            <p className="text-neutral-600 mb-6">
              We haven&apos;t published any help articles yet. Check back soon for updates!
            </p>
            <Link href="/">
              <Button variant="secondary">
                Back to Home
              </Button>
            </Link>
          </div>
        )}

        {searchTerm && articles.filter(article =>
          article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          article.excerpt?.toLowerCase().includes(searchTerm.toLowerCase())
        ).length === 0 && articles.length > 0 && (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-8 h-8 text-neutral-400" />
            </div>
            <h3 className="text-xl font-semibold text-neutral-900 mb-2">No results found</h3>
            <p className="text-neutral-600 mb-6">
              Try adjusting your search terms or browse all articles below.
            </p>
            <Button variant="secondary" onClick={() => setSearchTerm('')}>
              Clear Search
            </Button>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && !searchTerm && (
          <div className="mt-16 flex justify-center">
            <nav className="flex items-center space-x-2" aria-label="Pagination">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((p) => (
                <button
                  key={p}
                  onClick={() => setPage(p)}
                  className={`w-10 h-10 rounded-lg text-sm font-medium transition-colors ${
                    page === p
                      ? 'bg-primary-600 text-white shadow-md'
                      : 'bg-white text-neutral-600 hover:bg-neutral-100 border border-neutral-200'
                  }`}
                >
                  {p}
                </button>
              ))}
            </nav>
          </div>
        )}
      </div>
    </div>
  )
}