'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase-client'
import { useAuthContext } from '@/contexts/AuthContext'

export default function DebugAuthPage() {
  const { user, loading } = useAuthContext()
  const [clientSession, setClientSession] = useState<any>(null)
  const [cookies, setCookies] = useState<string>('')

  useEffect(() => {
    const checkClientSession = async () => {
      const supabase = createClient()
      const { data: { session }, error } = await supabase.auth.getSession()
      setClientSession(session)
      console.log('Client session:', session)
      console.log('Client session error:', error)
    }

    checkClientSession()
    setCookies(document.cookie)
  }, [])

  const testApiCall = async () => {
    try {
      const response = await fetch('/api/debug/session', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      const data = await response.json()
      console.log('API response:', data)
      alert('Check console for API response')
    } catch (error) {
      console.error('API call failed:', error)
      alert('API call failed - check console')
    }
  }

  return (
    <div className="min-h-screen bg-neutral-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Authentication Debug</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Auth Context</h2>
            <div className="space-y-2">
              <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
              <p><strong>User:</strong> {user ? user.email : 'None'}</p>
              <p><strong>Role:</strong> {user?.role || 'None'}</p>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Client Session</h2>
            <div className="space-y-2">
              <p><strong>Session exists:</strong> {clientSession ? 'Yes' : 'No'}</p>
              <p><strong>User email:</strong> {clientSession?.user?.email || 'None'}</p>
              <p><strong>User ID:</strong> {clientSession?.user?.id || 'None'}</p>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow md:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Browser Cookies</h2>
            <div className="bg-gray-100 p-4 rounded text-sm font-mono break-all">
              {cookies || 'No cookies found'}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow md:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Test API Call</h2>
            <button
              onClick={testApiCall}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Test Session API
            </button>
            <p className="text-sm text-gray-600 mt-2">
              This will call the debug session API and log the response to the console.
            </p>
          </div>
        </div>

        <div className="mt-8">
          <a
            href="/login"
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mr-4"
          >
            Go to Login
          </a>
          <a
            href="/admin/posts"
            className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
          >
            Go to Posts
          </a>
        </div>
      </div>
    </div>
  )
}
