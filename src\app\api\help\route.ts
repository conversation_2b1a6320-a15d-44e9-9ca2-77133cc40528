import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { supabaseAdmin } from '@/lib/supabase'
import { logger } from '@/lib/logger'
import { HelpArticle } from '@/types'

const querySchema = z.object({
  searchTerm: z.string().optional(),
  category: z.string().optional(),
  status: z.string().optional(),
  page: z.coerce.number().int().min(1).optional().default(1),
  limit: z.coerce.number().int().min(1).max(100).optional().default(10),
})

const createSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  excerpt: z.string().optional(),
  status: z.enum(['draft', 'published']).default('draft'),
  category_id: z.string().optional(),
  tag_ids: z.array(z.string()).optional().default([]),
  order_index: z.number().int().min(0).optional().default(0),
})

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      logger.warn('Unauthorized access attempt to help API', { ip: request.ip })
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: userRole } = await supabase.rpc('get_user_role', { user_id: session.user.id })

    if (userRole !== 'admin' && userRole !== 'editor') {
      logger.warn('Forbidden access attempt to help API', { userId: session.user.id, userRole })
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    const validation = querySchema.safeParse(queryParams)

    if (!validation.success) {
      logger.warn('Invalid query parameters for help API', { errors: validation.error.flatten() })
      return NextResponse.json({ error: 'Invalid query parameters', details: validation.error.flatten() }, { status: 400 })
    }

    const { searchTerm, category, status, page, limit } = validation.data
    const offset = (page - 1) * limit

    let query = supabase
      .from('help_articles')
      .select(`
        *,
        author:users(name, email),
        category:categories(name, slug),
        help_article_tags(tag:tags(id, name, slug))
      `, { count: 'exact' })

    if (searchTerm) {
      query = query.or(`title.ilike.%${searchTerm}%,content.ilike.%${searchTerm}%`)
    }

    if (category) {
      query = query.eq('category_id', category)
    }

    if (status) {
      query = query.eq('status', status)
    }

    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    const { data, error, count } = await query

    if (error) {
      logger.error('Error fetching help articles from Supabase', error)
      return NextResponse.json({ error: 'Failed to fetch help articles', details: error.message }, { status: 500 })
    }

    const totalPages = Math.ceil((count || 0) / limit)

    return NextResponse.json({
      articles: data,
      count: count || 0,
      totalPages,
      currentPage: page
    })

  } catch (error) {
    logger.error('Unexpected error in help API GET', error as Error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      logger.warn('Unauthorized access attempt to help API POST', { ip: request.ip })
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: userRole } = await supabase.rpc('get_user_role', { user_id: session.user.id })

    if (userRole !== 'admin' && userRole !== 'editor') {
      logger.warn('Forbidden access attempt to help API POST', { userId: session.user.id, userRole })
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validation = createSchema.safeParse(body)

    if (!validation.success) {
      logger.warn('Invalid help article data', { errors: validation.error.flatten() })
      return NextResponse.json({ error: 'Invalid help article data', details: validation.error.flatten() }, { status: 400 })
    }

    const { title, content, excerpt, status, category_id, tag_ids, order_index } = validation.data

    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Supabase admin client is not initialized.' }, { status: 500 })
    }

    // Generate slug from title
    const slug = `${title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')}-${Date.now()}`

    // Create the help article
    const { data: article, error: articleError } = await supabaseAdmin
      .from('help_articles')
      .insert({
        title,
        slug,
        content,
        excerpt,
        status,
        category_id: category_id || null,
        order_index,
        author_id: session.user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (articleError) {
      logger.error('Error creating help article', articleError)
      return NextResponse.json({ error: 'Failed to create help article', details: articleError.message }, { status: 500 })
    }

    // Add tags if provided
    if (tag_ids && tag_ids.length > 0) {
      const tagRelations = tag_ids.map(tagId => ({
        help_article_id: article.id,
        tag_id: tagId
      }))

      const { error: tagError } = await supabaseAdmin
        .from('help_article_tags')
        .insert(tagRelations)

      if (tagError) {
        logger.error('Error adding tags to help article', tagError)
        // Don't fail the request, just log the error
      }
    }

    logger.info('Help article created successfully', { articleId: article.id, userId: session.user.id })

    return NextResponse.json({ article }, { status: 201 })

  } catch (error) {
    logger.error('Unexpected error in help API POST', error as Error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
