'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  Save,
  Eye,
  ArrowLeft,
  Tag,
  Folder,
  FileText,
  AlertCircle,
  Image,
  Upload,
  X
} from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { Category, Tag as TagType, PostFormData } from '@/types'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuthContext } from '@/contexts/AuthContext'
import { generateSlug } from '@/lib/utils'
import RichTextEditor from '@/components/RichTextEditor'
import SEOFields from '@/components/SEOFields'
import TableOfContents from '@/components/TableOfContents'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardBody, CardHeader } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Alert } from '@/components/ui/Alert'

export default function CreatePostPage() {
  const router = useRouter()
  const { user } = useAuthContext()
  const [categories, setCategories] = useState<Category[]>([])
  const [tags, setTags] = useState<TagType[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState<PostFormData>({
    title: '',
    content: '',
    excerpt: '',
    featured_image: '',
    status: 'draft',
    category_id: '',
    tag_ids: [],
    meta_title: '',
    meta_description: '',
    canonical_url: '',
    social_image: '',
    scheduled_at: ''
  })
  const [tableOfContents, setTableOfContents] = useState<any[]>([])
  const [activeTab, setActiveTab] = useState<'content' | 'seo' | 'settings'>('content')

  useEffect(() => {
    fetchCategories()
    fetchTags()
  }, [])

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name')

      if (error) throw error
      setCategories(data || [])
    } catch (err) {
      console.error('Failed to fetch categories:', err)
    }
  }

  const fetchTags = async () => {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .order('name')

      if (error) throw error
      setTags(data || [])
    } catch (err) {
      console.error('Failed to fetch tags:', err)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    // Validate required fields
    if (!formData.title.trim()) {
      setError('Title is required')
      return
    }
    if (!formData.content.trim()) {
      setError('Content is required')
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Generate slug from title
      const slug = generateSlug(formData.title)

      // Check if slug already exists
      const { data: existingPost } = await supabase
        .from('posts')
        .select('id')
        .eq('slug', slug)
        .single()

      if (existingPost) {
        throw new Error('A post with this title already exists. Please choose a different title.')
      }

      // Generate excerpt if not provided
      const excerpt = formData.excerpt?.trim() ||
        formData.content.replace(/<[^>]*>/g, '').substring(0, 160) + '...'

      // Create the post with proper formatting
      const postData = {
        title: formData.title.trim(),
        slug,
        content: formData.content,
        excerpt: excerpt,
        featured_image: formData.featured_image?.trim() || null,
        status: formData.status,
        author_id: user.id,
        category_id: formData.category_id || null,
        published_at: formData.status === 'published' ? new Date().toISOString() : null,
        updated_at: new Date().toISOString(),
        version: 1
      }

      const { data: post, error: postError } = await supabase
        .from('posts')
        .insert([postData])
        .select()
        .single()

      if (postError) throw postError

      // Add tags if any selected
      if (formData.tag_ids && formData.tag_ids.length > 0) {
        const tagRelations = formData.tag_ids.map(tagId => ({
          post_id: post.id,
          tag_id: tagId
        }))

        const { error: tagError } = await supabase
          .from('post_tags')
          .insert(tagRelations)

        if (tagError) throw tagError
      }

      // Show success message and redirect
      router.push(`/admin/posts/${post.id}?created=true`)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create post')
    } finally {
      setLoading(false)
    }
  }

  const handleTagToggle = (tagId: string) => {
    setFormData(prev => ({
      ...prev,
      tag_ids: prev.tag_ids?.includes(tagId)
        ? prev.tag_ids.filter(id => id !== tagId)
        : [...(prev.tag_ids || []), tagId]
    }))
  }

  return (
    <div>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={() => router.back()}
                  leftIcon={<ArrowLeft className="w-4 h-4" />}
                >
                  Back
                </Button>
                <div>
                  <h1 className="text-3xl font-bold text-neutral-900">Create New Post</h1>
                  <p className="text-neutral-600 mt-1">Write and publish a new blog post</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  variant="secondary"
                  leftIcon={<Eye className="w-4 h-4" />}
                >
                  Preview
                </Button>
                <Button
                  type="submit"
                  form="post-form"
                  loading={loading}
                  leftIcon={<Save className="w-4 h-4" />}
                >
                  {loading ? 'Saving...' : 'Save Post'}
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert variant="error" className="mb-6" dismissible onDismiss={() => setError(null)}>
            {error}
          </Alert>
        )}

        <form id="post-form" onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Title */}
              <Card>
                <CardBody>
                  <Input
                    label="Post Title"
                    type="text"
                    required
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter post title..."
                    leftIcon={<FileText className="w-4 h-4" />}
                  />
                </CardBody>
              </Card>

              {/* Tabbed Content */}
              <Card>
                <CardHeader>
                  <div className="flex space-x-1 border-b border-gray-200">
                    <button
                      type="button"
                      onClick={() => setActiveTab('content')}
                      className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
                        activeTab === 'content'
                          ? 'bg-white text-primary-600 border-b-2 border-primary-600'
                          : 'text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      Content
                    </button>
                    <button
                      type="button"
                      onClick={() => setActiveTab('seo')}
                      className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
                        activeTab === 'seo'
                          ? 'bg-white text-primary-600 border-b-2 border-primary-600'
                          : 'text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      SEO & Meta
                    </button>
                    <button
                      type="button"
                      onClick={() => setActiveTab('settings')}
                      className={`px-4 py-2 text-sm font-medium rounded-t-lg ${
                        activeTab === 'settings'
                          ? 'bg-white text-primary-600 border-b-2 border-primary-600'
                          : 'text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      Settings
                    </button>
                  </div>
                </CardHeader>
                <CardBody>
                  {activeTab === 'content' && (
                    <div className="space-y-6">
                      <RichTextEditor
                        value={formData.content}
                        onChange={(value) => setFormData(prev => ({ ...prev, content: value }))}
                        placeholder="Write your professional blog post content here..."
                        height="600px"
                        showWordCount={true}
                        showReadingTime={true}
                        enableTableOfContents={true}
                        onTableOfContentsChange={setTableOfContents}
                      />

                      {/* Table of Contents Preview */}
                      {tableOfContents.length > 0 && (
                        <TableOfContents
                          content={formData.content}
                          onTOCChange={setTableOfContents}
                        />
                      )}
                    </div>
                  )}

                  {activeTab === 'seo' && (
                    <SEOFields
                      title={formData.title}
                      content={formData.content}
                      metaTitle={formData.meta_title || ''}
                      metaDescription={formData.meta_description || ''}
                      canonicalUrl={formData.canonical_url || ''}
                      socialImage={formData.social_image || ''}
                      onMetaTitleChange={(value) => setFormData(prev => ({ ...prev, meta_title: value }))}
                      onMetaDescriptionChange={(value) => setFormData(prev => ({ ...prev, meta_description: value }))}
                      onCanonicalUrlChange={(value) => setFormData(prev => ({ ...prev, canonical_url: value }))}
                      onSocialImageChange={(value) => setFormData(prev => ({ ...prev, social_image: value }))}
                    />
                  )}

                  {activeTab === 'settings' && (
                    <div className="space-y-6">
                      {/* Excerpt */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Excerpt
                        </label>
                        <textarea
                          value={formData.excerpt || ''}
                          onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                          placeholder="Write a brief excerpt for your post..."
                          rows={4}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>

                      {/* Featured Image */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Featured Image URL
                        </label>
                        <input
                          type="url"
                          value={formData.featured_image || ''}
                          onChange={(e) => setFormData(prev => ({ ...prev, featured_image: e.target.value }))}
                          placeholder="https://example.com/image.jpg"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>

                      {/* Scheduled Publishing */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Schedule Publication
                        </label>
                        <input
                          type="datetime-local"
                          value={formData.scheduled_at || ''}
                          onChange={(e) => setFormData(prev => ({ ...prev, scheduled_at: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                        <p className="text-sm text-gray-500 mt-1">
                          Leave empty to publish immediately
                        </p>
                      </div>
                    </div>
                  )}
                </CardBody>
              </Card>

              {/* Excerpt */}
              <Card>
                <CardBody>
                  <div className="form-group">
                    <label className="form-label">Excerpt (Optional)</label>
                    <textarea
                      rows={3}
                      value={formData.excerpt}
                      onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                      className="form-textarea"
                      placeholder="Brief description of the post..."
                    />
                    <p className="text-sm text-neutral-500 mt-2">
                  A short summary that will be displayed in post listings and social media shares.
                </p>
              </div>
            </CardBody>
            </Card>

              {/* Featured Image */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <label htmlFor="featured_image" className="block text-sm font-medium text-gray-700 mb-2">
                  Featured Image URL (Optional)
                </label>
                <input
                  type="url"
                  id="featured_image"
                  value={formData.featured_image}
                  onChange={(e) => setFormData(prev => ({ ...prev, featured_image: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="https://example.com/image.jpg"
                />
                <p className="text-sm text-gray-500 mt-2">
                  URL of the image to display as the post&apos;s featured image.
                </p>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Publish Settings */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  Publish Settings
                </h3>
                
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    id="status"
                    value={formData.status}
                    onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="draft">Draft</option>
                    <option value="published">Published</option>
                  </select>
                </div>
              </div>

              {/* Category */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Folder className="w-5 h-5 mr-2" />
                  Category
                </h3>
                
                <select
                  value={formData.category_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, category_id: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Tags */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Tag className="w-5 h-5 mr-2" />
                  Tags
                </h3>
                
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {tags.map(tag => (
                    <label key={tag.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.tag_ids?.includes(tag.id) || false}
                        onChange={() => handleTagToggle(tag.id)}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{tag.name}</span>
                    </label>
                  ))}
                </div>
                
                {tags.length === 0 && (
                  <p className="text-sm text-gray-500">No tags available</p>
                )}
              </div>

              {/* Actions */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div className="space-y-3">
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {loading ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    ) : (
                      <Save className="w-4 h-4 mr-2" />
                    )}
                    {loading ? 'Creating...' : 'Create Post'}
                  </button>
                  
                  <button
                    type="button"
                    onClick={() => router.back()}
                    className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}