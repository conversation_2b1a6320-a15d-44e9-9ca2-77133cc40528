// User and Authentication Types
export interface User {
  id: string
  email: string
  role: 'admin' | 'editor' | 'viewer'
  name?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

// Content Types
export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  created_at: string
  updated_at: string
}

export interface Tag {
  id: string
  name: string
  slug: string
  color?: string
  created_at: string
}

export interface Post {
  id: string
  title: string
  slug: string
  content: string
  excerpt?: string
  featured_image?: string
  featured_image_url?: string
  status: 'draft' | 'published' | 'archived'
  author_id: string
  category_id?: string
  tags?: Tag[]
  published_at?: string
  created_at: string
  updated_at: string
  version: number
  
  // Relations
  author?: User
  category?: Category
}

export interface HelpArticle {
  id: string
  title: string
  slug: string
  content: string
  excerpt?: string
  status: 'draft' | 'published' | 'archived'
  author_id: string
  category_id?: string
  tags?: Tag[]
  order_index?: number
  published_at?: string
  created_at: string
  updated_at: string
  version: number
  
  // Relations
  author?: User
  category?: Category
}

export interface MediaFile {
  id: string
  filename: string
  original_name: string
  file_path: string
  file_size: number
  mime_type: string
  alt_text?: string
  description?: string
  folder_path?: string
  download_count: number
  uploaded_by: string
  created_at: string
  updated_at: string
  
  // Relations
  uploader?: User
}

// Content Versioning
export interface ContentVersion {
  id: string
  content_id: string
  content_type: 'post' | 'help_article'
  version_number: number
  title: string
  content: string
  changes_summary?: string
  created_by: string
  created_at: string
  
  // Relations
  author?: User
}

// Search and Analytics
export interface SearchResult {
  id: string
  title: string
  excerpt: string
  type: 'post' | 'help_article' | 'file'
  url: string
  relevance_score: number
}

export interface AnalyticsData {
  total_posts: number
  total_help_articles: number
  total_files: number
  total_downloads: number
  recent_activity: ActivityItem[]
}

export interface ActivityItem {
  id: string
  type: 'post_created' | 'post_updated' | 'help_created' | 'help_updated' | 'file_uploaded'
  title: string
  user_name: string
  created_at: string
}

// API Response Types
export interface ApiResponse<T> {
  data: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    total_pages: number
  }
}

// Form Types
export interface PostFormData {
  title: string
  content: string
  excerpt?: string
  featured_image?: string
  status: 'draft' | 'published'
  category_id?: string
  tag_ids?: string[]
  meta_title?: string
  meta_description?: string
  canonical_url?: string
  social_image?: string
  scheduled_at?: string
}

export interface HelpArticleFormData {
  title: string
  content: string
  excerpt?: string
  status: 'draft' | 'published'
  category_id?: string
  tag_ids?: string[]
  order_index?: number
}

export interface CategoryFormData {
  name: string
  description?: string
  color?: string
}

export interface TagFormData {
  name: string
  color?: string
}