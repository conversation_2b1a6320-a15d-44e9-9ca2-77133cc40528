import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { queryKeys } from '@/lib/react-query'
import { HelpArticle } from '@/types'

interface HelpArticlesQueryParams {
  page?: number
  limit?: number
  searchTerm?: string
  category?: string
  status?: string
}

interface HelpArticlesResponse {
  articles: HelpArticle[]
  count: number
  totalPages: number
  currentPage: number
}

// Fetch help articles with caching
export function useHelpArticlesQuery(params: HelpArticlesQueryParams = {}) {
  return useQuery({
    queryKey: queryKeys.helpArticles.list(params),
    queryFn: async (): Promise<HelpArticlesResponse> => {
      const searchParams = new URLSearchParams({
        page: (params.page || 1).toString(),
        limit: (params.limit || 10).toString(),
        searchTerm: params.searchTerm || '',
        category: params.category || '',
        status: params.status || '',
      })

      const response = await fetch(`/api/help?${searchParams.toString()}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch help articles')
      }

      return response.json()
    },
    // Enable background refetching
    refetchOnWindowFocus: true,
    // Stale time of 3 minutes for help articles
    staleTime: 3 * 60 * 1000,
  })
}

// Fetch single help article
export function useHelpArticleQuery(id: string) {
  return useQuery({
    queryKey: queryKeys.helpArticles.detail(id),
    queryFn: async (): Promise<HelpArticle> => {
      const response = await fetch(`/api/help/${id}`, {
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch help article')
      }

      const data = await response.json()
      return data.article
    },
    enabled: !!id,
    // Cache individual articles for longer
    staleTime: 5 * 60 * 1000,
  })
}

// Delete help article mutation
export function useDeleteHelpArticleMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (articleId: string) => {
      const response = await fetch(`/api/help/${articleId}`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete help article')
      }

      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch help articles queries
      queryClient.invalidateQueries({ queryKey: queryKeys.helpArticles.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.all })
    },
  })
}

// Create/Update help article mutation
export function useHelpArticleMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, data }: { id?: string; data: any }) => {
      const url = id ? `/api/help/${id}` : '/api/help'
      const method = id ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to ${id ? 'update' : 'create'} help article`)
      }

      return response.json()
    },
    onSuccess: (data, variables) => {
      // Invalidate help articles queries
      queryClient.invalidateQueries({ queryKey: queryKeys.helpArticles.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.all })
      
      // Update the specific article in cache if it was an update
      if (variables.id && data.article) {
        queryClient.setQueryData(queryKeys.helpArticles.detail(variables.id), data.article)
      }
    },
  })
}
