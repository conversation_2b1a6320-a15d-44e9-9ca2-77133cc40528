'use client'

import { useState, useEffect } from 'react'
import { Search, Globe, Eye, Hash, Clock, TrendingUp } from 'lucide-react'

interface SEOFieldsProps {
  title: string
  content: string
  metaTitle: string
  metaDescription: string
  canonicalUrl: string
  socialImage: string
  onMetaTitleChange: (value: string) => void
  onMetaDescriptionChange: (value: string) => void
  onCanonicalUrlChange: (value: string) => void
  onSocialImageChange: (value: string) => void
}

export default function SEOFields({
  title,
  content,
  metaTitle,
  metaDescription,
  canonicalUrl,
  socialImage,
  onMetaTitleChange,
  onMetaDescriptionChange,
  onCanonicalUrlChange,
  onSocialImageChange
}: SEOFieldsProps) {
  const [seoScore, setSeoScore] = useState(0)
  const [seoSuggestions, setSeoSuggestions] = useState<string[]>([])
  const [readingTime, setReadingTime] = useState(0)
  const [wordCount, setWordCount] = useState(0)

  // Calculate SEO metrics
  useEffect(() => {
    const calculateSEOMetrics = () => {
      // Calculate word count and reading time
      const text = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim()
      const words = text.split(' ').filter(word => word.length > 0).length
      const readingTimeMinutes = Math.ceil(words / 200)
      
      setWordCount(words)
      setReadingTime(readingTimeMinutes)

      // Calculate SEO score
      let score = 0
      const suggestions: string[] = []

      // Title checks
      if (title.length >= 30 && title.length <= 60) {
        score += 20
      } else if (title.length < 30) {
        suggestions.push('Title is too short. Aim for 30-60 characters.')
      } else {
        suggestions.push('Title is too long. Keep it under 60 characters.')
      }

      // Meta title checks
      if (metaTitle.length >= 50 && metaTitle.length <= 60) {
        score += 15
      } else if (metaTitle.length === 0) {
        suggestions.push('Add a meta title for better SEO.')
      } else if (metaTitle.length < 50) {
        suggestions.push('Meta title is too short. Aim for 50-60 characters.')
      } else {
        suggestions.push('Meta title is too long. Keep it under 60 characters.')
      }

      // Meta description checks
      if (metaDescription.length >= 150 && metaDescription.length <= 160) {
        score += 20
      } else if (metaDescription.length === 0) {
        suggestions.push('Add a meta description for better SEO.')
      } else if (metaDescription.length < 150) {
        suggestions.push('Meta description is too short. Aim for 150-160 characters.')
      } else {
        suggestions.push('Meta description is too long. Keep it under 160 characters.')
      }

      // Content length checks
      if (words >= 300 && words <= 2000) {
        score += 15
      } else if (words < 300) {
        suggestions.push('Content is too short. Aim for at least 300 words.')
      } else {
        suggestions.push('Content is very long. Consider breaking it into multiple posts.')
      }

      // Heading structure check
      const headingMatches = content.match(/<h[1-6][^>]*>/g)
      if (headingMatches && headingMatches.length >= 2) {
        score += 10
      } else {
        suggestions.push('Add more headings to improve content structure.')
      }

      // Image check
      const imageMatches = content.match(/<img[^>]*>/g)
      if (imageMatches && imageMatches.length >= 1) {
        score += 10
      } else {
        suggestions.push('Add images to make your content more engaging.')
      }

      // Social image check
      if (socialImage) {
        score += 10
      } else {
        suggestions.push('Add a social media image for better sharing.')
      }

      setSeoScore(score)
      setSeoSuggestions(suggestions)
    }

    calculateSEOMetrics()
  }, [title, content, metaTitle, metaDescription, socialImage])

  const getSEOScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100'
    if (score >= 60) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getSEOScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent'
    if (score >= 60) return 'Good'
    if (score >= 40) return 'Needs Improvement'
    return 'Poor'
  }

  return (
    <div className="space-y-6">
      {/* SEO Score Overview */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            SEO Analysis
          </h3>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${getSEOScoreColor(seoScore)}`}>
            {seoScore}/100 - {getSEOScoreLabel(seoScore)}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{wordCount}</div>
            <div className="text-sm text-gray-600">Words</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{readingTime}</div>
            <div className="text-sm text-gray-600">Min Read</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{title.length}</div>
            <div className="text-sm text-gray-600">Title Length</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{metaDescription.length}</div>
            <div className="text-sm text-gray-600">Meta Desc</div>
          </div>
        </div>

        {/* SEO Suggestions */}
        {seoSuggestions.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">SEO Suggestions:</h4>
            <ul className="space-y-1">
              {seoSuggestions.map((suggestion, index) => (
                <li key={index} className="text-sm text-gray-600 flex items-start">
                  <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                  {suggestion}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* SEO Fields */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Search className="w-5 h-5 mr-2" />
          SEO Settings
        </h3>

        <div className="space-y-4">
          {/* Meta Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Meta Title
              <span className="text-gray-500 ml-1">({metaTitle.length}/60)</span>
            </label>
            <input
              type="text"
              value={metaTitle}
              onChange={(e) => onMetaTitleChange(e.target.value)}
              placeholder={title || "Enter meta title for search engines"}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              maxLength={60}
            />
            <div className="mt-1 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={`h-full transition-all duration-300 ${
                  metaTitle.length <= 60 ? 'bg-green-500' : 'bg-red-500'
                }`}
                style={{ width: `${Math.min((metaTitle.length / 60) * 100, 100)}%` }}
              />
            </div>
          </div>

          {/* Meta Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Meta Description
              <span className="text-gray-500 ml-1">({metaDescription.length}/160)</span>
            </label>
            <textarea
              value={metaDescription}
              onChange={(e) => onMetaDescriptionChange(e.target.value)}
              placeholder="Write a compelling description that will appear in search results"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              maxLength={160}
            />
            <div className="mt-1 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={`h-full transition-all duration-300 ${
                  metaDescription.length <= 160 ? 'bg-green-500' : 'bg-red-500'
                }`}
                style={{ width: `${Math.min((metaDescription.length / 160) * 100, 100)}%` }}
              />
            </div>
          </div>

          {/* Canonical URL */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
              <Globe className="w-4 h-4 mr-1" />
              Canonical URL
            </label>
            <input
              type="url"
              value={canonicalUrl}
              onChange={(e) => onCanonicalUrlChange(e.target.value)}
              placeholder="https://example.com/blog/post-slug"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          {/* Social Image */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
              <Eye className="w-4 h-4 mr-1" />
              Social Media Image
            </label>
            <input
              type="url"
              value={socialImage}
              onChange={(e) => onSocialImageChange(e.target.value)}
              placeholder="https://example.com/images/social-image.jpg"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
            {socialImage && (
              <div className="mt-2">
                <img 
                  src={socialImage} 
                  alt="Social media preview" 
                  className="w-full max-w-md h-32 object-cover rounded-lg border border-gray-200"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none'
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
