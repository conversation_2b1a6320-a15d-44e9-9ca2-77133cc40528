'use client'

import { useState, useEffect, useCallback } from 'react'
import Image from 'next/image'
import { supabase } from '@/lib/supabase'
import { MediaFile } from '@/types'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuthContext } from '@/contexts/AuthContext'
import { Upload, Trash2, File, Image as ImageIcon, Video, Music, AlertCircle } from 'lucide-react'

export default function MediaPage() {
  const { canEdit } = useAuthContext()
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)

  const fetchMedia = useCallback(async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('media_files')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setMediaFiles(data || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch media files')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchMedia()
  }, [fetchMedia])

  const validateFile = (file: File): string | null => {
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      return 'File size must be less than 10MB'
    }

    // Check file type
    const allowedTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
      'video/mp4', 'video/webm', 'video/ogg',
      'audio/mp3', 'audio/wav', 'audio/ogg',
      'application/pdf', 'text/plain', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]

    if (!allowedTypes.includes(file.type)) {
      return 'File type not supported. Please upload images, videos, audio files, or documents.'
    }

    return null
  }

  const generateUniqueFilename = (originalName: string): string => {
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 8)
    const extension = originalName.split('.').pop()
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '')
    return `${nameWithoutExt}_${timestamp}_${randomString}.${extension}`
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) {
      return
    }

    const file = event.target.files[0]

    // Validate file
    const validationError = validateFile(file)
    if (validationError) {
      setError(validationError)
      return
    }

    setUploading(true)
    setError(null)

    try {
      // Generate unique filename to prevent overwrites
      const uniqueFilename = generateUniqueFilename(file.name)
      const filePath = `public/${uniqueFilename}`

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('media')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (uploadError) throw uploadError

      const { data: publicUrlData } = supabase.storage
        .from('media')
        .getPublicUrl(uploadData.path)

      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const newMediaFile = {
        filename: uniqueFilename,
        original_name: file.name,
        file_path: publicUrlData.publicUrl,
        file_size: file.size,
        mime_type: file.type,
        uploaded_by: user.id,
        alt_text: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const { data: insertedFile, error: dbError } = await supabase
        .from('media_files')
        .insert([newMediaFile])
        .select()
        .single()

      if (dbError) throw dbError

      // Add to state immediately for better UX
      setMediaFiles(prev => [insertedFile, ...prev])

      // Reset file input
      event.target.value = ''
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload file')
    } finally {
      setUploading(false)
    }
  }

  const deleteMedia = async (file: MediaFile) => {
    if (!confirm(`Are you sure you want to delete "${file.original_name || file.filename}"? This action cannot be undone.`)) return

    try {
      // Extract the path from the file_path URL or use filename
      let filePath = `public/${file.filename}`

      // If file_path contains the full URL, extract the path
      if (file.file_path && file.file_path.includes('/storage/v1/object/public/media/')) {
        const pathParts = file.file_path.split('/storage/v1/object/public/media/')
        if (pathParts.length > 1) {
          filePath = pathParts[1]
        }
      }

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('media')
        .remove([filePath])

      if (storageError) {
        console.warn('Storage deletion failed:', storageError)
        // Continue with database deletion even if storage fails
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from('media_files')
        .delete()
        .eq('id', file.id)

      if (dbError) throw dbError

      // Remove from state
      setMediaFiles(prev => prev.filter(mf => mf.id !== file.id))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete file')
    }
  }

  const updateMediaAltText = async (fileId: string, altText: string) => {
    try {
      const { error } = await supabase
        .from('media_files')
        .update({
          alt_text: altText.trim() || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', fileId)

      if (error) throw error

      // Update state
      setMediaFiles(prev => prev.map(file =>
        file.id === fileId
          ? { ...file, alt_text: altText.trim() || undefined }
          : file
      ))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update alt text')
    }
  }

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <ImageIcon className="w-12 h-12 text-gray-500" />
    if (mimeType.startsWith('video/')) return <Video className="w-12 h-12 text-gray-500" />
    if (mimeType.startsWith('audio/')) return <Music className="w-12 h-12 text-gray-500" />
    return <File className="w-12 h-12 text-gray-500" />
  }

  return (
    <ProtectedRoute requiredRole={['admin', 'editor']}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Media Library</h1>
            <p className="text-gray-600 mt-1">Manage your uploaded files</p>
          </div>
          {canEdit() && (
            <label className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors cursor-pointer">
              <Upload className="w-4 h-4 mr-2" />
              {uploading ? 'Uploading...' : 'Upload File'}
              <input
                type="file"
                className="hidden"
                onChange={handleFileUpload}
                disabled={uploading}
              />
            </label>
          )}
        </div>

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
            <AlertCircle className="w-5 h-5 text-red-500 mr-3 mt-0.5" />
            <div>
              <p className="text-red-800">{error}</p>
              <button onClick={() => setError(null)} className="mt-2 text-sm text-red-600 hover:text-red-800">
                Dismiss
              </button>
            </div>
          </div>
        )}

        {loading ? (
          <div className="text-center py-12">Loading media...</div>
        ) : mediaFiles.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg shadow-sm border">
            <p className="text-gray-500">No media files found. Upload your first file!</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {mediaFiles.map(file => (
              <div key={file.id} className="bg-white rounded-lg shadow-sm border overflow-hidden group relative">
                <div className="aspect-w-1 aspect-h-1 bg-gray-100 flex items-center justify-center relative">
                  {file.mime_type.startsWith('image/') ? (
                    <Image src={file.file_path} alt={file.alt_text || file.filename} className="object-cover" fill sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" />
                  ) : (
                    getFileIcon(file.mime_type)
                  )}
                </div>
                <div className="p-2 text-xs">
                  <p className="font-medium text-gray-800 truncate">{file.filename}</p>
                  <p className="text-gray-500">{new Date(file.created_at).toLocaleDateString()}</p>
                </div>
                {canEdit() && (
                  <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={() => deleteMedia(file)}
                      className="p-1.5 bg-red-600 text-white rounded-full hover:bg-red-700 shadow-md"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </ProtectedRoute>
  )
}