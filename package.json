{"name": "eria-cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "migrate": "node scripts/run-migration.js", "setup": "npm install && npm run migrate"}, "dependencies": {"@headlessui/react": "^1.7.17", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.38.4", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-quill": "^2.0.0", "quill-better-table": "^1.2.10", "quill-image-resize-module-react": "^3.0.0", "tailwind-merge": "^2.1.0", "zod": "^4.0.10"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.21", "dotenv": "^16.3.1", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.3.3"}}