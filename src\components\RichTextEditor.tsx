'use client'

import dynamic from 'next/dynamic'
import { Suspense } from 'react'
import LoadingSpinner from './LoadingSpinner'

// Dynamically import ReactQuill with loading state
const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => (
    <div className="h-96 bg-white border border-gray-300 rounded-lg flex items-center justify-center">
      <LoadingSpinner text="Loading editor..." />
    </div>
  )
})

// Dynamically import the CSS to reduce initial bundle size
dynamic(() => import('react-quill/dist/quill.snow.css'), { ssr: false })

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
}

export default function RichTextEditor({ value, onChange, placeholder }: RichTextEditorProps) {
  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{'list': 'ordered'}, {'list': 'bullet'}],
      ['link', 'image', 'video'],
      ['clean']
    ],
  }

  return (
    <Suspense fallback={
      <div className="h-96 bg-white border border-gray-300 rounded-lg flex items-center justify-center">
        <LoadingSpinner text="Loading editor..." />
      </div>
    }>
      <div className="bg-white">
        <ReactQuill
          theme="snow"
          value={value}
          onChange={onChange}
          modules={modules}
          placeholder={placeholder}
          className="h-96"
        />
      </div>
    </Suspense>
  )
}