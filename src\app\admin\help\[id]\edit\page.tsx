'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { 
  Save, 
  ArrowLeft, 
  Tag, 
  Folder,
  HelpCircle,
  AlertCircle,
  Trash2
} from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { Category, Tag as TagType, HelpArticleFormData, HelpArticle } from '@/types'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuthContext } from '@/contexts/AuthContext'
import { generateSlug } from '@/lib/utils'
import RichTextEditor from '@/components/RichTextEditor'

export default function EditHelpArticlePage() {
  const router = useRouter()
  const params = useParams()
  const { id } = params
  const { user } = useAuthContext()

  const [article, setArticle] = useState<HelpArticle | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const [tags, setTags] = useState<TagType[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState<HelpArticleFormData>({
    title: '',
    content: '',
    excerpt: '',
    status: 'draft',
    category_id: '',
    tag_ids: [],
    order_index: 0
  })



  const fetchArticleData = useCallback(async () => {
    if (!id) return
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('help_articles')
        .select('*, help_article_tags(tag_id)')
        .eq('id', id)
        .single()

      if (error) throw error
      
      setArticle(data)
      setFormData({
        title: data.title,
        content: data.content,
        excerpt: data.excerpt || '',
        status: data.status,
        category_id: data.category_id || '',
        tag_ids: data.help_article_tags?.map((at: any) => at.tag_id) || [],
        order_index: data.order_index || 0
      })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch help article data')
    } finally {
      setLoading(false)
    }
  }, [id])

  const fetchCategories = useCallback(async () => {
    try {
      const { data, error } = await supabase.from('categories').select('*').order('name')
      if (error) throw error
      setCategories(data || [])
    } catch (err) {
      console.error('Failed to fetch categories:', err)
    }
  }, [])

  const fetchTags = useCallback(async () => {
    try {
      const { data, error } = await supabase.from('tags').select('*').order('name')
      if (error) throw error
      setTags(data || [])
    } catch (err) {
      console.error('Failed to fetch tags:', err)
    }
  }, [])

  useEffect(() => {
    fetchArticleData()
    fetchCategories()
    fetchTags()
  }, [fetchArticleData, fetchCategories, fetchTags])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !article) return

    // Validate required fields
    if (!formData.title.trim()) {
      setError('Title is required')
      return
    }
    if (!formData.content.trim()) {
      setError('Content is required')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const slug = generateSlug(formData.title)

      // Check if slug already exists for a DIFFERENT article
      const { data: existingArticle } = await supabase
        .from('help_articles')
        .select('id')
        .eq('slug', slug)
        .not('id', 'eq', article.id)
        .single()

      if (existingArticle) {
        throw new Error('A help article with this title already exists. Please choose a different title.')
      }

      // Generate excerpt if not provided
      const excerpt = formData.excerpt?.trim() || 
        formData.content.replace(/<[^>]*>/g, '').substring(0, 160) + '...'

      // Increment version for tracking changes
      const newVersion = (article.version || 1) + 1

      const articleData = {
        title: formData.title.trim(),
        slug,
        content: formData.content,
        excerpt: excerpt,
        status: formData.status,
        category_id: formData.category_id || null,
        order_index: formData.order_index || 0,
        published_at: formData.status === 'published' && !article.published_at ? new Date().toISOString() : article.published_at,
        updated_at: new Date().toISOString(),
        version: newVersion
      }

      const { error: articleError } = await supabase
        .from('help_articles')
        .update(articleData)
        .eq('id', article.id)

      if (articleError) throw articleError

      // Update tags - delete existing and add new ones
      const { error: deleteTagError } = await supabase
        .from('help_article_tags')
        .delete()
        .eq('help_article_id', article.id)

      if (deleteTagError) throw deleteTagError

      if (formData.tag_ids && formData.tag_ids.length > 0) {
        const tagRelations = formData.tag_ids.map(tagId => ({
          help_article_id: article.id,
          tag_id: tagId
        }))
        const { error: tagError } = await supabase
          .from('help_article_tags')
          .insert(tagRelations)
        if (tagError) throw tagError
      }

      router.push(`/admin/help/${article.id}?updated=true`)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update help article')
    } finally {
      setLoading(false)
    }
  }

  const deleteArticle = async () => {
    if (!article || !confirm('Are you sure you want to delete this help article?')) return

    setLoading(true)
    try {
      const { error } = await supabase.from('help_articles').delete().eq('id', article.id)
      if (error) throw error
      router.push('/admin/help')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete help article')
      setLoading(false)
    }
  }

  const handleTagToggle = (tagId: string) => {
    setFormData(prev => ({
      ...prev,
      tag_ids: prev.tag_ids?.includes(tagId)
        ? prev.tag_ids.filter(id => id !== tagId)
        : [...(prev.tag_ids || []), tagId]
    }))
  }

  if (loading && !article) {
    return <div>Loading...</div>
  }

  if (!article) {
    return <div>Help article not found.</div>
  }

  return (
    <ProtectedRoute requiredRole={['admin', 'editor']}>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <button onClick={() => router.back()} className="flex items-center text-gray-600 hover:text-gray-900 mr-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Help Articles
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Edit Help Article</h1>
              <p className="text-gray-600 mt-1">Update your help article</p>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
            <AlertCircle className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-red-800">{error}</p>
              <button onClick={() => setError(null)} className="mt-2 text-sm text-red-600 hover:text-red-800">
                Dismiss
              </button>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Main Content */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="space-y-6">
              {/* Title */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Title *
                </label>
                <input
                  type="text"
                  id="title"
                  required
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter help article title..."
                />
              </div>

              {/* Excerpt */}
              <div>
                <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 mb-2">
                  Excerpt
                </label>
                <textarea
                  id="excerpt"
                  rows={3}
                  value={formData.excerpt}
                  onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Brief description of the help article (optional - will be auto-generated if empty)"
                />
              </div>

              {/* Content */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content *
                </label>
                <RichTextEditor
                  value={formData.content}
                  onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                  placeholder="Write your help article content here..."
                />
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              {/* Tags */}
              {tags.length > 0 && (
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <div className="flex items-center mb-4">
                    <Tag className="w-5 h-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">Tags</h3>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {tags.map(tag => (
                      <button
                        key={tag.id}
                        type="button"
                        onClick={() => handleTagToggle(tag.id)}
                        className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                          formData.tag_ids?.includes(tag.id)
                            ? 'bg-primary-100 text-primary-800 border border-primary-200'
                            : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                        }`}
                      >
                        {tag.name}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-6">
              {/* Publish Settings */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Publish Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                      Status
                    </label>
                    <select
                      id="status"
                      value={formData.status}
                      onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="draft">Draft</option>
                      <option value="published">Published</option>
                      <option value="archived">Archived</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                      Category
                    </label>
                    <select
                      id="category"
                      value={formData.category_id}
                      onChange={(e) => setFormData(prev => ({ ...prev, category_id: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">Select Category</option>
                      {categories.map(category => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="order_index" className="block text-sm font-medium text-gray-700 mb-2">
                      Order Index
                    </label>
                    <input
                      type="number"
                      id="order_index"
                      min="0"
                      value={formData.order_index}
                      onChange={(e) => setFormData(prev => ({ ...prev, order_index: parseInt(e.target.value) || 0 }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="0"
                    />
                    <p className="text-sm text-gray-500 mt-1">Lower numbers appear first</p>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div className="flex flex-col space-y-3">
                  <button
                    type="submit"
                    disabled={loading}
                    className="flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? 'Saving...' : 'Save Changes'}
                    {!loading && <Save className="w-4 h-4 ml-2" />}
                  </button>
                  <button
                    type="button"
                    onClick={deleteArticle}
                    disabled={loading}
                    className="flex items-center justify-center px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 disabled:opacity-50"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Article
                  </button>
                  <button
                    type="button"
                    onClick={() => router.back()}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </ProtectedRoute>
  )
}
