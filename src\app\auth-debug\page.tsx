'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase-client'

export default function AuthDebugPage() {
  const [authData, setAuthData] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const searchParams = useSearchParams()
  const supabase = createClient()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Get current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        
        // Get URL parameters
        const urlParams = {
          access_token: searchParams.get('access_token') ? 'Present (hidden for security)' : 'Missing',
          refresh_token: searchParams.get('refresh_token') ? 'Present (hidden for security)' : 'Missing',
          expires_in: searchParams.get('expires_in'),
          token_type: searchParams.get('token_type'),
          token_hash: searchParams.get('token_hash'),
          type: searchParams.get('type'),
          error: searchParams.get('error'),
          error_description: searchParams.get('error_description')
        }

        // Test session creation if we have tokens
        let sessionTest = null
        const accessToken = searchParams.get('access_token')
        const refreshToken = searchParams.get('refresh_token')

        if (accessToken && refreshToken) {
          try {
            const { data, error: sessionError } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken
            })
            sessionTest = {
              success: !sessionError,
              error: sessionError?.message,
              hasSession: !!data.session,
              userId: data.session?.user?.id
            }
          } catch (err) {
            sessionTest = {
              success: false,
              error: err instanceof Error ? err.message : 'Unknown error',
              hasSession: false
            }
          }
        }

        setAuthData({
          session,
          sessionError,
          urlParams,
          sessionTest,
          currentUrl: window.location.href
        })

        if (sessionError) {
          setError(sessionError.message)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
      }
    }

    checkAuth()
  }, [searchParams, supabase.auth])

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold mb-6">Authentication Debug</h1>
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded p-4 mb-6">
              <h3 className="font-semibold text-red-800">Error:</h3>
              <p className="text-red-700">{error}</p>
            </div>
          )}

          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-semibold mb-2">Current Session</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(authData?.session, null, 2)}
              </pre>
            </div>

            <div>
              <h2 className="text-lg font-semibold mb-2">URL Parameters</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(authData?.urlParams, null, 2)}
              </pre>
            </div>

            <div>
              <h2 className="text-lg font-semibold mb-2">Session Test</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(authData?.sessionTest, null, 2)}
              </pre>
            </div>

            <div>
              <h2 className="text-lg font-semibold mb-2">Current URL</h2>
              <p className="bg-gray-100 p-4 rounded text-sm break-all">
                {authData?.currentUrl}
              </p>
            </div>

            <div>
              <h2 className="text-lg font-semibold mb-2">Environment Variables</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify({
                  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
                  NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
                  NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME
                }, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
