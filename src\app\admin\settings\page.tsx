'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuthContext } from '@/contexts/AuthContext'
import { Save, AlertCircle } from 'lucide-react'

interface Settings {
  site_title: string
  site_description: string
  posts_per_page: number
}

export default function SettingsPage() {
  const { user } = useAuthContext()
  const [settings, setSettings] = useState<Settings>({
    site_title: '',
    site_description: '',
    posts_per_page: 10,
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('settings')
        .select('*')

      if (error) throw error

      if (data) {
        const newSettings: any = {}
        data.forEach(setting => {
          newSettings[setting.key] = setting.value
        })
        setSettings(newSettings)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch settings')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setSettings(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    setError(null)

    try {
      const updates = Object.entries(settings).map(([key, value]) => ({
        key,
        value: String(value),
      }))

      for (const update of updates) {
        const { error } = await supabase
          .from('settings')
          .upsert(update, { onConflict: 'key' })
        
        if (error) throw error
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <div>Loading settings...</div>
  }

  return (
    <ProtectedRoute requiredRole="admin">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">Settings</h1>
        
        {error && (
          <div className="p-4 mb-6 bg-red-50 border border-red-200 rounded-lg flex items-start">
            <AlertCircle className="w-5 h-5 text-red-500 mr-3 mt-0.5" />
            <div>
              <p className="text-red-800">{error}</p>
              <button onClick={() => setError(null)} className="mt-2 text-sm text-red-600 hover:text-red-800">
                Dismiss
              </button>
            </div>
          </div>
        )}

        <div className="bg-white p-8 rounded-lg shadow-md border">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="site_title" className="block text-sm font-medium text-gray-700 mb-2">
                Site Title
              </label>
              <input
                type="text"
                id="site_title"
                name="site_title"
                value={settings.site_title}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div>
              <label htmlFor="site_description" className="block text-sm font-medium text-gray-700 mb-2">
                Site Description
              </label>
              <textarea
                id="site_description"
                name="site_description"
                rows={3}
                value={settings.site_description}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div>
              <label htmlFor="posts_per_page" className="block text-sm font-medium text-gray-700 mb-2">
                Posts Per Page
              </label>
              <input
                type="number"
                id="posts_per_page"
                name="posts_per_page"
                min="1"
                value={settings.posts_per_page}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div className="flex justify-end pt-4">
              <button
                type="submit"
                disabled={saving}
                className="flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50"
              >
                <Save className="w-4 h-4 mr-2" />
                {saving ? 'Saving...' : 'Save Settings'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </ProtectedRoute>
  )
}