// Performance monitoring utilities

export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number> = new Map()

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  // Start timing an operation
  startTiming(key: string): void {
    this.metrics.set(key, performance.now())
  }

  // End timing and log the result
  endTiming(key: string, logToConsole = true): number {
    const startTime = this.metrics.get(key)
    if (!startTime) {
      console.warn(`No start time found for key: ${key}`)
      return 0
    }

    const duration = performance.now() - startTime
    this.metrics.delete(key)

    if (logToConsole) {
      console.log(`⏱️ ${key}: ${duration.toFixed(2)}ms`)
    }

    return duration
  }

  // Measure a function execution time
  async measureAsync<T>(key: string, fn: () => Promise<T>): Promise<T> {
    this.startTiming(key)
    try {
      const result = await fn()
      this.endTiming(key)
      return result
    } catch (error) {
      this.endTiming(key)
      throw error
    }
  }

  // Measure sync function execution time
  measure<T>(key: string, fn: () => T): T {
    this.startTiming(key)
    try {
      const result = fn()
      this.endTiming(key)
      return result
    } catch (error) {
      this.endTiming(key)
      throw error
    }
  }

  // Log Core Web Vitals
  logWebVitals(): void {
    if (typeof window === 'undefined') return

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      console.log('🎯 LCP:', lastEntry.startTime.toFixed(2) + 'ms')
    }).observe({ entryTypes: ['largest-contentful-paint'] })

    // First Input Delay
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        console.log('⚡ FID:', entry.processingStart - entry.startTime + 'ms')
      })
    }).observe({ entryTypes: ['first-input'] })

    // Cumulative Layout Shift
    new PerformanceObserver((list) => {
      let clsValue = 0
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      })
      console.log('📐 CLS:', clsValue.toFixed(4))
    }).observe({ entryTypes: ['layout-shift'] })
  }
}

// Singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance()

// React hook for performance monitoring
export function usePerformanceMonitor() {
  return performanceMonitor
}

// Debounce function for performance optimization
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

// Throttle function for performance optimization
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Lazy loading utility
export function createIntersectionObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver | null {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    return null
  }

  return new IntersectionObserver(callback, {
    rootMargin: '50px',
    threshold: 0.1,
    ...options,
  })
}

// Memory usage monitoring
export function logMemoryUsage(): void {
  if (typeof window === 'undefined' || !('performance' in window)) return

  const memory = (performance as any).memory
  if (memory) {
    console.log('💾 Memory Usage:', {
      used: `${Math.round(memory.usedJSHeapSize / 1048576)} MB`,
      total: `${Math.round(memory.totalJSHeapSize / 1048576)} MB`,
      limit: `${Math.round(memory.jsHeapSizeLimit / 1048576)} MB`,
    })
  }
}
