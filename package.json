{"name": "eria-cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.17", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.38.4", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-quill": "^2.0.0", "tailwind-merge": "^2.1.0", "zod": "^4.0.10"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.3.3"}}