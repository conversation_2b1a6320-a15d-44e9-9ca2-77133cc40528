'use client'

import { useState, useEffect } from 'react'
import { Hash, ChevronRight, ChevronDown, List } from 'lucide-react'

interface TOCItem {
  id: string
  level: number
  text: string
  anchor: string
}

interface TableOfContentsProps {
  content: string
  onTOCChange?: (toc: TOCItem[]) => void
  showInSidebar?: boolean
  className?: string
}

export default function TableOfContents({ 
  content, 
  onTOCChange, 
  showInSidebar = false,
  className = ""
}: TableOfContentsProps) {
  const [toc, setToc] = useState<TOCItem[]>([])
  const [activeId, setActiveId] = useState<string>('')
  const [isCollapsed, setIsCollapsed] = useState(false)

  // Generate table of contents from HTML content
  useEffect(() => {
    const generateTOC = () => {
      if (!content) {
        setToc([])
        return
      }

      const parser = new DOMParser()
      const doc = parser.parseFromString(content, 'text/html')
      const headings = doc.querySelectorAll('h1, h2, h3, h4, h5, h6')
      
      const tocItems: TOCItem[] = Array.from(headings).map((heading, index) => {
        const text = heading.textContent || ''
        const level = parseInt(heading.tagName.charAt(1))
        const anchor = text
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim()
          .replace(/^-|-$/g, '') || `heading-${index}`

        return {
          id: `toc-${index}`,
          level,
          text,
          anchor
        }
      })

      setToc(tocItems)
      if (onTOCChange) {
        onTOCChange(tocItems)
      }
    }

    generateTOC()
  }, [content, onTOCChange])

  // Handle scroll to section
  const scrollToSection = (anchor: string) => {
    const element = document.getElementById(anchor)
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      })
      setActiveId(anchor)
    }
  }

  // Track active section on scroll
  useEffect(() => {
    if (!showInSidebar) return

    const handleScroll = () => {
      const headings = toc.map(item => document.getElementById(item.anchor)).filter(Boolean)
      
      for (let i = headings.length - 1; i >= 0; i--) {
        const heading = headings[i]
        if (heading && heading.getBoundingClientRect().top <= 100) {
          setActiveId(heading.id)
          break
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [toc, showInSidebar])

  if (toc.length === 0) {
    return null
  }

  const renderTOCItem = (item: TOCItem, index: number) => {
    const isActive = activeId === item.anchor
    const indentLevel = Math.max(0, item.level - 1)
    
    return (
      <li key={item.id} style={{ marginLeft: `${indentLevel * 16}px` }}>
        <button
          onClick={() => scrollToSection(item.anchor)}
          className={`
            w-full text-left py-2 px-3 rounded-md text-sm transition-colors duration-200
            ${isActive 
              ? 'bg-primary-100 text-primary-800 font-medium' 
              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }
          `}
        >
          <span className="flex items-center">
            {item.level === 1 && <Hash className="w-3 h-3 mr-2 flex-shrink-0" />}
            <span className="truncate">{item.text}</span>
          </span>
        </button>
      </li>
    )
  }

  if (showInSidebar) {
    return (
      <div className={`sticky top-8 ${className}`}>
        <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-gray-900 flex items-center">
              <List className="w-4 h-4 mr-2" />
              Table of Contents
            </h3>
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-1 hover:bg-gray-100 rounded"
            >
              {isCollapsed ? (
                <ChevronRight className="w-4 h-4 text-gray-500" />
              ) : (
                <ChevronDown className="w-4 h-4 text-gray-500" />
              )}
            </button>
          </div>
          
          {!isCollapsed && (
            <nav>
              <ul className="space-y-1 max-h-96 overflow-y-auto">
                {toc.map(renderTOCItem)}
              </ul>
            </nav>
          )}
        </div>
      </div>
    )
  }

  // Inline TOC for editor
  return (
    <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
      <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
        <Hash className="w-4 h-4 mr-2" />
        Table of Contents Preview
      </h4>
      
      {toc.length > 0 ? (
        <nav>
          <ul className="space-y-1">
            {toc.map((item, index) => (
              <li key={item.id} style={{ marginLeft: `${Math.max(0, item.level - 1) * 16}px` }}>
                <div className="py-1 px-2 text-sm text-gray-600 bg-white rounded border">
                  <span className="flex items-center">
                    {item.level === 1 && <Hash className="w-3 h-3 mr-2 flex-shrink-0" />}
                    <span className="truncate">{item.text}</span>
                    <span className="ml-auto text-xs text-gray-400">H{item.level}</span>
                  </span>
                </div>
              </li>
            ))}
          </ul>
        </nav>
      ) : (
        <p className="text-sm text-gray-500 italic">
          Add headings to your content to generate a table of contents
        </p>
      )}
    </div>
  )
}

// Hook for generating TOC data
export function useTableOfContents(content: string) {
  const [toc, setToc] = useState<TOCItem[]>([])

  useEffect(() => {
    if (!content) {
      setToc([])
      return
    }

    const parser = new DOMParser()
    const doc = parser.parseFromString(content, 'text/html')
    const headings = doc.querySelectorAll('h1, h2, h3, h4, h5, h6')
    
    const tocItems: TOCItem[] = Array.from(headings).map((heading, index) => {
      const text = heading.textContent || ''
      const level = parseInt(heading.tagName.charAt(1))
      const anchor = text
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
        .replace(/^-|-$/g, '') || `heading-${index}`

      return {
        id: `toc-${index}`,
        level,
        text,
        anchor
      }
    })

    setToc(tocItems)
  }, [content])

  return toc
}

// Component for adding TOC to rendered content
export function TOCRenderer({ toc, className = "" }: { toc: TOCItem[], className?: string }) {
  if (toc.length === 0) return null

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 ${className}`}>
      <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center">
        <List className="w-5 h-5 mr-2" />
        Table of Contents
      </h3>
      <nav>
        <ul className="space-y-2">
          {toc.map((item) => (
            <li key={item.id} style={{ marginLeft: `${Math.max(0, item.level - 1) * 20}px` }}>
              <a
                href={`#${item.anchor}`}
                className="text-blue-700 hover:text-blue-900 hover:underline text-sm flex items-center py-1"
              >
                {item.level === 1 && <Hash className="w-3 h-3 mr-2 flex-shrink-0" />}
                {item.text}
              </a>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  )
}
