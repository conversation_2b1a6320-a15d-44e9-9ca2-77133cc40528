import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { supabaseAdmin } from '@/lib/supabase'
import { logger } from '@/lib/logger'

const querySchema = z.object({
  page: z.coerce.number().int().min(1).optional().default(1),
  limit: z.coerce.number().int().min(1).max(100).optional().default(10),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    const validation = querySchema.safeParse(queryParams)

    if (!validation.success) {
      logger.warn('Invalid query parameters for public posts API', { errors: validation.error.flatten() })
      return NextResponse.json({ error: 'Invalid query parameters', details: validation.error.flatten() }, { status: 400 })
    }

    const { page, limit } = validation.data
    const offset = (page - 1) * limit

    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Supabase admin client is not initialized.' }, { status: 500 })
    }

    const query = supabaseAdmin
      .from('posts')
      .select(`
        *,
        author:users(name, email),
        category:categories(name, slug)
      `, { count: 'exact' })
      .eq('status', 'published')
      .order('published_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data, error, count } = await query

    if (error) {
      logger.error('Error fetching public posts from Supabase', error)
      return NextResponse.json({ error: 'Failed to fetch posts', details: error.message }, { status: 500 })
    }

    return NextResponse.json({
      posts: data,
      count: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    })
  } catch (err) {
    const error = err instanceof Error ? err : new Error('An unexpected error occurred')
    logger.error('An unexpected error occurred in public posts API', error)
    return NextResponse.json({ error: 'Server error', details: error.message }, { status: 500 })
  }
}