import { useQuery } from '@tanstack/react-query'
import { queryKeys } from '@/lib/react-query'
import { createClient } from '@/lib/supabase-client'
import { Post, HelpArticle } from '@/types'

interface DashboardStats {
  totalPosts: number
  totalHelpArticles: number
  totalMediaFiles: number
  totalUsers: number
}

// Fetch dashboard stats with caching
export function useDashboardStatsQuery(isAdmin: () => boolean) {
  return useQuery({
    queryKey: queryKeys.dashboard.stats(),
    queryFn: async (): Promise<DashboardStats> => {
      const supabase = createClient()

      // Fetch stats in parallel for better performance
      const [postsCount, helpCount, mediaCount, usersCount] = await Promise.all([
        supabase.from('posts').select('id', { count: 'exact', head: true }),
        supabase.from('help_articles').select('id', { count: 'exact', head: true }),
        supabase.from('media_files').select('id', { count: 'exact', head: true }),
        isAdmin() ? supabase.from('users').select('id', { count: 'exact', head: true }) : { count: 0 }
      ])

      return {
        totalPosts: postsCount.count || 0,
        totalHelpArticles: helpCount.count || 0,
        totalMediaFiles: mediaCount.count || 0,
        totalUsers: usersCount.count || 0
      }
    },
    // Cache stats for 5 minutes since they don't change frequently
    staleTime: 5 * 60 * 1000,
    // Refetch on window focus to keep stats current
    refetchOnWindowFocus: true,
  })
}

// Fetch recent posts for dashboard
export function useRecentPostsQuery() {
  return useQuery({
    queryKey: queryKeys.dashboard.recentPosts(),
    queryFn: async (): Promise<Post[]> => {
      const supabase = createClient()
      
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          author:users(name, email),
          category:categories(name)
        `)
        .order('created_at', { ascending: false })
        .limit(5)

      if (error) throw error
      return data || []
    },
    // Cache recent posts for 2 minutes
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  })
}

// Fetch recent help articles for dashboard
export function useRecentHelpArticlesQuery() {
  return useQuery({
    queryKey: queryKeys.dashboard.recentHelpArticles(),
    queryFn: async (): Promise<HelpArticle[]> => {
      const supabase = createClient()
      
      const { data, error } = await supabase
        .from('help_articles')
        .select(`
          *,
          author:users(name, email),
          category:categories(name)
        `)
        .order('created_at', { ascending: false })
        .limit(5)

      if (error) throw error
      return data || []
    },
    // Cache recent help articles for 2 minutes
    staleTime: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
  })
}

// Combined dashboard data hook for convenience
export function useDashboardData(isAdmin: () => boolean) {
  const statsQuery = useDashboardStatsQuery(isAdmin)
  const recentPostsQuery = useRecentPostsQuery()
  const recentHelpArticlesQuery = useRecentHelpArticlesQuery()

  return {
    stats: statsQuery.data,
    recentPosts: recentPostsQuery.data,
    recentHelpArticles: recentHelpArticlesQuery.data,
    isLoading: statsQuery.isLoading || recentPostsQuery.isLoading || recentHelpArticlesQuery.isLoading,
    error: statsQuery.error || recentPostsQuery.error || recentHelpArticlesQuery.error,
    refetch: () => {
      statsQuery.refetch()
      recentPostsQuery.refetch()
      recentHelpArticlesQuery.refetch()
    }
  }
}
