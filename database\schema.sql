-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('admin', 'editor', 'viewer');
CREATE TYPE content_status AS ENUM ('draft', 'published', 'archived');
CREATE TYPE content_type AS ENUM ('post', 'help_article');

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    role user_role DEFAULT 'viewer',
    name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories table
CREATE TABLE IF NOT EXISTS public.categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    color TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tags table
CREATE TABLE IF NOT EXISTS public.tags (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    color TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Posts table
CREATE TABLE IF NOT EXISTS public.posts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    featured_image TEXT,
    status content_status DEFAULT 'draft',
    author_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES public.categories(id) ON DELETE SET NULL,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    version INTEGER DEFAULT 1
);

-- Help articles table
CREATE TABLE IF NOT EXISTS public.help_articles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    status content_status DEFAULT 'draft',
    author_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES public.categories(id) ON DELETE SET NULL,
    order_index INTEGER,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    version INTEGER DEFAULT 1
);

-- Media files table
CREATE TABLE IF NOT EXISTS public.media_files (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    filename TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type TEXT NOT NULL,
    alt_text TEXT,
    description TEXT,
    folder_path TEXT,
    download_count INTEGER DEFAULT 0,
    uploaded_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Settings table
CREATE TABLE IF NOT EXISTS public.settings (
    key TEXT PRIMARY KEY,
    value TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Content versions table (for version control)
CREATE TABLE IF NOT EXISTS public.content_versions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_id UUID NOT NULL,
    content_type content_type NOT NULL,
    version_number INTEGER NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    changes_summary TEXT,
    created_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- Recent activity table
CREATE TABLE IF NOT EXISTS public.recent_activity (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Junction tables for many-to-many relationships
CREATE TABLE IF NOT EXISTS public.post_tags (
    post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES public.tags(id) ON DELETE CASCADE,
    PRIMARY KEY (post_id, tag_id)
);

CREATE TABLE IF NOT EXISTS public.help_article_tags (
    help_article_id UUID REFERENCES public.help_articles(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES public.tags(id) ON DELETE CASCADE,
    PRIMARY KEY (help_article_id, tag_id)
);

-- Create indexes for better performance
CREATE INDEX idx_posts_author_id ON public.posts(author_id);
CREATE INDEX idx_posts_category_id ON public.posts(category_id);
CREATE INDEX idx_posts_status ON public.posts(status);
CREATE INDEX idx_posts_published_at ON public.posts(published_at);
CREATE INDEX idx_posts_slug ON public.posts(slug);

CREATE INDEX idx_help_articles_author_id ON public.help_articles(author_id);
CREATE INDEX idx_help_articles_category_id ON public.help_articles(category_id);
CREATE INDEX idx_help_articles_status ON public.help_articles(status);
CREATE INDEX idx_help_articles_published_at ON public.help_articles(published_at);
CREATE INDEX idx_help_articles_slug ON public.help_articles(slug);
CREATE INDEX idx_help_articles_order_index ON public.help_articles(order_index);

CREATE INDEX idx_media_files_uploaded_by ON public.media_files(uploaded_by);
CREATE INDEX idx_media_files_mime_type ON public.media_files(mime_type);
CREATE INDEX idx_media_files_folder_path ON public.media_files(folder_path);

CREATE INDEX idx_content_versions_content_id ON public.content_versions(content_id);
CREATE INDEX idx_content_versions_content_type ON public.content_versions(content_type);

-- Full-text search indexes
CREATE INDEX idx_posts_search ON public.posts USING gin(to_tsvector('english', title || ' ' || content));
CREATE INDEX idx_help_articles_search ON public.help_articles USING gin(to_tsvector('english', title || ' ' || content));

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON public.settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Apply updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON public.categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_posts_updated_at BEFORE UPDATE ON public.posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_help_articles_updated_at BEFORE UPDATE ON public.help_articles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_media_files_updated_at BEFORE UPDATE ON public.media_files FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.help_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.media_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.help_article_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recent_activity ENABLE ROW LEVEL SECURITY;

-- Create function to safely check user roles (SECURITY DEFINER bypasses RLS)
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID DEFAULT auth.uid())
RETURNS TEXT
LANGUAGE SQL
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT role::TEXT FROM public.users WHERE id = user_id;
$$;

-- Users policies
CREATE POLICY "Users can view their own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON public.users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can view all users" ON public.users FOR SELECT USING (
    public.get_user_role() = 'admin'
);

-- Categories policies (public read, admin/editor write)
CREATE POLICY "Anyone can view published categories" ON public.categories FOR SELECT USING (true);
CREATE POLICY "Admins and editors can manage categories" ON public.categories FOR ALL USING (
    public.get_user_role() IN ('admin', 'editor')
);

-- Tags policies (public read, admin/editor write)
CREATE POLICY "Anyone can view tags" ON public.tags FOR SELECT USING (true);
CREATE POLICY "Admins and editors can manage tags" ON public.tags FOR ALL USING (
    public.get_user_role() IN ('admin', 'editor')
);

-- Posts policies
CREATE POLICY "Anyone can view published posts" ON public.posts FOR SELECT USING (status = 'published');
CREATE POLICY "Authors can view their own posts" ON public.posts FOR SELECT USING (auth.uid() = author_id);
CREATE POLICY "Admins can view all posts" ON public.posts FOR SELECT USING (
    public.get_user_role() = 'admin'
);
CREATE POLICY "Authors can create posts" ON public.posts FOR INSERT WITH CHECK (auth.uid() = author_id);
CREATE POLICY "Authors can update their own posts" ON public.posts FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Admins can update all posts" ON public.posts FOR UPDATE USING (
    public.get_user_role() = 'admin'
);

-- Help articles policies
CREATE POLICY "Anyone can view published help articles" ON public.help_articles FOR SELECT USING (status = 'published');
CREATE POLICY "Authors can view their own help articles" ON public.help_articles FOR SELECT USING (auth.uid() = author_id);
CREATE POLICY "Admins can view all help articles" ON public.help_articles FOR SELECT USING (
    public.get_user_role() = 'admin'
);
CREATE POLICY "Authors can create help articles" ON public.help_articles FOR INSERT WITH CHECK (auth.uid() = author_id);
CREATE POLICY "Authors can update their own help articles" ON public.help_articles FOR UPDATE USING (auth.uid() = author_id);
CREATE POLICY "Admins can update all help articles" ON public.help_articles FOR UPDATE USING (
    public.get_user_role() = 'admin'
);

-- Media files policies
CREATE POLICY "Anyone can view media files" ON public.media_files FOR SELECT USING (true);
CREATE POLICY "Authenticated users can upload files" ON public.media_files FOR INSERT WITH CHECK (auth.uid() = uploaded_by);
CREATE POLICY "Uploaders can update their own files" ON public.media_files FOR UPDATE USING (auth.uid() = uploaded_by);
CREATE POLICY "Admins can manage all files" ON public.media_files FOR ALL USING (
    public.get_user_role() = 'admin'
);

-- Content versions policies
CREATE POLICY "Authors can view versions of their content" ON public.content_versions FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.posts WHERE id = content_id AND author_id = auth.uid()
        UNION
        SELECT 1 FROM public.help_articles WHERE id = content_id AND author_id = auth.uid()
    )
);
-- Settings policies
CREATE POLICY "Anyone can view settings" ON public.settings FOR SELECT USING (true);
CREATE POLICY "Admins can manage settings" ON public.settings FOR ALL USING (
    public.get_user_role() = 'admin'
);
-- Recent activity policies
CREATE POLICY "Authenticated users can view recent activity" ON public.recent_activity FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Admins can view all content versions" ON public.content_versions FOR SELECT USING (
    public.get_user_role() = 'admin'
);

-- Junction table policies
CREATE POLICY "Anyone can view post tags" ON public.post_tags FOR SELECT USING (true);
CREATE POLICY "Authors can manage their post tags" ON public.post_tags FOR ALL USING (
    EXISTS (SELECT 1 FROM public.posts WHERE id = post_id AND author_id = auth.uid())
);

CREATE POLICY "Anyone can view help article tags" ON public.help_article_tags FOR SELECT USING (true);
CREATE POLICY "Authors can manage their help article tags" ON public.help_article_tags FOR ALL USING (
    EXISTS (SELECT 1 FROM public.help_articles WHERE id = help_article_id AND author_id = auth.uid())
);

-- Admin utility functions for user management

-- Function to safely promote a user to admin (can only be called by existing admins)
CREATE OR REPLACE FUNCTION promote_user_to_admin(target_user_id UUID, target_email TEXT DEFAULT NULL)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    current_user_role TEXT;
    target_user_exists BOOLEAN;
    result JSON;
BEGIN
    -- Check if current user is admin
    SELECT role INTO current_user_role
    FROM public.users
    WHERE id = auth.uid();
    
    IF current_user_role != 'admin' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Only admins can promote users'
        );
    END IF;
    
    -- Check if target user exists (by ID or email)
    IF target_user_id IS NOT NULL THEN
        SELECT EXISTS(SELECT 1 FROM public.users WHERE id = target_user_id) INTO target_user_exists;
        
        IF target_user_exists THEN
            UPDATE public.users
            SET role = 'admin', updated_at = NOW()
            WHERE id = target_user_id;
            
            RETURN json_build_object(
                'success', true,
                'message', 'User promoted to admin successfully',
                'user_id', target_user_id
            );
        END IF;
    ELSIF target_email IS NOT NULL THEN
        SELECT EXISTS(SELECT 1 FROM public.users WHERE email = target_email) INTO target_user_exists;
        
        IF target_user_exists THEN
            UPDATE public.users
            SET role = 'admin', updated_at = NOW()
            WHERE email = target_email
            RETURNING id INTO target_user_id;
            
            RETURN json_build_object(
                'success', true,
                'message', 'User promoted to admin successfully',
                'user_id', target_user_id,
                'email', target_email
            );
        END IF;
    END IF;
    
    RETURN json_build_object(
        'success', false,
        'error', 'User not found'
    );
END;
$$;

-- Function to bulk update user roles
CREATE OR REPLACE FUNCTION bulk_update_user_roles(role_updates JSON)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    current_user_role TEXT;
    update_record JSON;
    updated_count INTEGER := 0;
    error_count INTEGER := 0;
    errors TEXT[] := ARRAY[]::TEXT[];
BEGIN
    -- Check if current user is admin
    SELECT role INTO current_user_role
    FROM public.users
    WHERE id = auth.uid();
    
    IF current_user_role != 'admin' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Only admins can bulk update user roles'
        );
    END IF;
    
    -- Process each update
    FOR update_record IN SELECT * FROM json_array_elements(role_updates)
    LOOP
        BEGIN
            UPDATE public.users
            SET role = (update_record->>'role')::user_role,
                updated_at = NOW()
            WHERE id = (update_record->>'user_id')::UUID;
            
            IF FOUND THEN
                updated_count := updated_count + 1;
            ELSE
                error_count := error_count + 1;
                errors := array_append(errors, 'User not found: ' || (update_record->>'user_id'));
            END IF;
        EXCEPTION WHEN OTHERS THEN
            error_count := error_count + 1;
            errors := array_append(errors, 'Error updating user ' || (update_record->>'user_id') || ': ' || SQLERRM);
        END;
    END LOOP;
    
    RETURN json_build_object(
        'success', error_count = 0,
        'updated_count', updated_count,
        'error_count', error_count,
        'errors', errors
    );
END;
$$;

-- Function to get user statistics for admin dashboard
CREATE OR REPLACE FUNCTION get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    current_user_role TEXT;
    stats JSON;
BEGIN
    -- Check if current user is admin
    SELECT role INTO current_user_role
    FROM public.users
    WHERE id = auth.uid();
    
    IF current_user_role != 'admin' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Only admins can view user statistics'
        );
    END IF;
    
    SELECT json_build_object(
        'success', true,
        'data', json_build_object(
            'total_users', (SELECT COUNT(*) FROM public.users),
            'admin_count', (SELECT COUNT(*) FROM public.users WHERE role = 'admin'),
            'editor_count', (SELECT COUNT(*) FROM public.users WHERE role = 'editor'),
            'viewer_count', (SELECT COUNT(*) FROM public.users WHERE role = 'viewer'),
            'recent_signups', (
                SELECT json_agg(
                    json_build_object(
                        'id', id,
                        'email', email,
                        'name', name,
                        'role', role,
                        'created_at', created_at
                    )
                )
                FROM (
                    SELECT id, email, name, role, created_at
                    FROM public.users
                    ORDER BY created_at DESC
                    LIMIT 10
                ) recent
            )
        )
    ) INTO stats;
    
    RETURN stats;
END;
$$;

-- Function to create the first admin user (can only be used when no admins exist)
CREATE OR REPLACE FUNCTION create_first_admin(admin_email TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    admin_count INTEGER;
    user_exists BOOLEAN;
BEGIN
    -- Check if any admin users already exist
    SELECT COUNT(*) INTO admin_count
    FROM public.users
    WHERE role = 'admin';
    
    IF admin_count > 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Admin users already exist. Use promote_user_to_admin instead.'
        );
    END IF;
    
    -- Check if user exists
    SELECT EXISTS(SELECT 1 FROM public.users WHERE email = admin_email) INTO user_exists;
    
    IF NOT user_exists THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User with email ' || admin_email || ' does not exist'
        );
    END IF;
    
    -- Promote user to admin
    UPDATE public.users
    SET role = 'admin', updated_at = NOW()
    WHERE email = admin_email;
    
    RETURN json_build_object(
        'success', true,
        'message', 'First admin user created successfully',
        'email', admin_email
    );
END;
$$;

-- Grant execute permissions to authenticated users (RLS will handle authorization)
GRANT EXECUTE ON FUNCTION promote_user_to_admin(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION bulk_update_user_roles(JSON) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION create_first_admin(TEXT) TO authenticated;

-- Insert default categories
INSERT INTO public.categories (name, slug, description, color) VALUES
('General', 'general', 'General blog posts and articles', '#3B82F6'),
('Technology', 'technology', 'Technology-related content', '#10B981'),
('Tutorials', 'tutorials', 'Step-by-step tutorials and guides', '#F59E0B'),
('News', 'news', 'Latest news and updates', '#EF4444');

-- Insert default tags
INSERT INTO public.tags (name, slug, color) VALUES
('Getting Started', 'getting-started', '#3B82F6'),
('FAQ', 'faq', '#10B981'),
('Troubleshooting', 'troubleshooting', '#F59E0B'),
('Advanced', 'advanced', '#EF4444'),
('Tips', 'tips', '#8B5CF6');