enum LogLevel {
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: Record<string, any>;
}

class Logger {
  private log(level: LogLevel, message: string, context?: Record<string, any>) {
    const logEntry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context,
    };

    // In a real application, you would send this to a logging service
    // like Sentry, LogRocket, or a custom logging endpoint.
    console.log(JSON.stringify(logEntry, null, 2));
  }

  info(message: string, context?: Record<string, any>) {
    this.log(LogLevel.INFO, message, context);
  }

  warn(message: string, context?: Record<string, any>) {
    this.log(LogLevel.WARN, message, context);
  }

  error(message: string, error: Error, context?: Record<string, any>) {
    this.log(LogLevel.ERROR, message, {
      ...context,
      errorMessage: error.message,
      stack: error.stack,
    });
  }
}

export const logger = new Logger();