#!/usr/bin/env node

/**
 * Database Migration Runner
 * 
 * This script runs database migrations safely on your live system.
 * It connects to your Supabase database and executes the migration files.
 * 
 * Usage:
 *   node scripts/run-migration.js
 * 
 * Environment Variables Required:
 *   - SUPABASE_URL: Your Supabase project URL
 *   - SUPABASE_SERVICE_ROLE_KEY: Your service role key (not anon key!)
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   - NEXT_PUBLIC_SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  console.error('')
  console.error('Make sure your .env.local file contains these values.')
  process.exit(1)
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function runMigration(migrationFile) {
  console.log(`\n🔄 Running migration: ${migrationFile}`)
  
  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', migrationFile)
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSQL })
    
    if (error) {
      console.error(`❌ Migration failed: ${error.message}`)
      return false
    }
    
    console.log(`✅ Migration completed successfully: ${migrationFile}`)
    return true
    
  } catch (err) {
    console.error(`❌ Error running migration ${migrationFile}:`, err.message)
    return false
  }
}

async function createExecSqlFunction() {
  console.log('🔧 Setting up exec_sql function...')
  
  const execSqlFunction = `
    CREATE OR REPLACE FUNCTION exec_sql(sql TEXT)
    RETURNS TEXT
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      EXECUTE sql;
      RETURN 'Success';
    EXCEPTION
      WHEN OTHERS THEN
        RETURN 'Error: ' || SQLERRM;
    END;
    $$;
  `
  
  try {
    const { error } = await supabase.rpc('exec', { sql: execSqlFunction })
    if (error && !error.message.includes('already exists')) {
      // Try alternative approach
      const { error: altError } = await supabase
        .from('_migrations')
        .select('*')
        .limit(1)
      
      if (altError) {
        console.log('⚠️  Could not create exec_sql function. Running migrations directly...')
        return false
      }
    }
    console.log('✅ exec_sql function ready')
    return true
  } catch (err) {
    console.log('⚠️  Could not create exec_sql function. Running migrations directly...')
    return false
  }
}

async function runMigrationDirect(migrationFile) {
  console.log(`\n🔄 Running migration directly: ${migrationFile}`)
  
  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', migrationFile)
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    // Split SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(`   Executing ${statements.length} SQL statements...`)
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      if (statement.trim()) {
        try {
          const { error } = await supabase.rpc('exec', { sql: statement })
          if (error) {
            console.warn(`   ⚠️  Statement ${i + 1} warning: ${error.message}`)
          }
        } catch (err) {
          console.warn(`   ⚠️  Statement ${i + 1} error: ${err.message}`)
        }
      }
    }
    
    console.log(`✅ Migration completed: ${migrationFile}`)
    return true
    
  } catch (err) {
    console.error(`❌ Error running migration ${migrationFile}:`, err.message)
    return false
  }
}

async function main() {
  console.log('🚀 Starting database migration...')
  console.log(`📍 Target database: ${supabaseUrl}`)
  
  // Test connection
  try {
    const { data, error } = await supabase.from('users').select('id').limit(1)
    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows found" which is OK
      throw error
    }
    console.log('✅ Database connection successful')
  } catch (err) {
    console.error('❌ Database connection failed:', err.message)
    process.exit(1)
  }
  
  // Get list of migration files
  const migrationsDir = path.join(__dirname, '..', 'database', 'migrations')
  
  if (!fs.existsSync(migrationsDir)) {
    console.error('❌ Migrations directory not found:', migrationsDir)
    process.exit(1)
  }
  
  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort()
  
  if (migrationFiles.length === 0) {
    console.log('ℹ️  No migration files found')
    return
  }
  
  console.log(`📁 Found ${migrationFiles.length} migration file(s):`)
  migrationFiles.forEach(file => console.log(`   - ${file}`))
  
  // Setup exec function
  const hasExecFunction = await createExecSqlFunction()
  
  // Run migrations
  let successCount = 0
  for (const migrationFile of migrationFiles) {
    const success = hasExecFunction 
      ? await runMigration(migrationFile)
      : await runMigrationDirect(migrationFile)
    
    if (success) {
      successCount++
    }
  }
  
  console.log(`\n🎉 Migration process completed!`)
  console.log(`   ✅ Successful: ${successCount}/${migrationFiles.length}`)
  
  if (successCount === migrationFiles.length) {
    console.log('\n🚀 All migrations completed successfully!')
    console.log('   Your database is now optimized for better performance.')
    console.log('\n📋 Next steps:')
    console.log('   1. Install new npm packages: npm install')
    console.log('   2. Restart your application')
    console.log('   3. Test the improved performance')
  } else {
    console.log('\n⚠️  Some migrations had issues. Please check the logs above.')
    console.log('   The migrations are designed to be safe and idempotent.')
    console.log('   You can run this script again to retry failed migrations.')
  }
}

// Handle errors gracefully
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

// Run the migration
main().catch(err => {
  console.error('❌ Migration failed:', err.message)
  process.exit(1)
})
