import { QueryClient } from '@tanstack/react-query'

// Create a client with optimized defaults
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache data for 5 minutes
      staleTime: 5 * 60 * 1000,
      // Keep data in cache for 10 minutes
      gcTime: 10 * 60 * 1000,
      // Retry failed requests 2 times
      retry: 2,
      // Don't refetch on window focus by default
      refetchOnWindowFocus: false,
      // Don't refetch on reconnect by default
      refetchOnReconnect: false,
    },
    mutations: {
      // Retry failed mutations once
      retry: 1,
    },
  },
})

// Query keys for consistent caching
export const queryKeys = {
  posts: {
    all: ['posts'] as const,
    lists: () => [...queryKeys.posts.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.posts.lists(), filters] as const,
    details: () => [...queryKeys.posts.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.posts.details(), id] as const,
  },
  categories: {
    all: ['categories'] as const,
    lists: () => [...queryKeys.categories.all, 'list'] as const,
    list: (filters?: Record<string, any>) => [...queryKeys.categories.lists(), filters] as const,
  },
  helpArticles: {
    all: ['help-articles'] as const,
    lists: () => [...queryKeys.helpArticles.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.helpArticles.lists(), filters] as const,
    details: () => [...queryKeys.helpArticles.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.helpArticles.details(), id] as const,
  },
  dashboard: {
    all: ['dashboard'] as const,
    stats: () => [...queryKeys.dashboard.all, 'stats'] as const,
    recentPosts: () => [...queryKeys.dashboard.all, 'recent-posts'] as const,
    recentHelpArticles: () => [...queryKeys.dashboard.all, 'recent-help-articles'] as const,
  },
  media: {
    all: ['media'] as const,
    lists: () => [...queryKeys.media.all, 'list'] as const,
    list: (filters?: Record<string, any>) => [...queryKeys.media.lists(), filters] as const,
  },
  auth: {
    all: ['auth'] as const,
    user: () => [...queryKeys.auth.all, 'user'] as const,
  },
} as const

// Helper function to invalidate related queries
export const invalidateQueries = {
  posts: () => queryClient.invalidateQueries({ queryKey: queryKeys.posts.all }),
  categories: () => queryClient.invalidateQueries({ queryKey: queryKeys.categories.all }),
  helpArticles: () => queryClient.invalidateQueries({ queryKey: queryKeys.helpArticles.all }),
  dashboard: () => queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.all }),
  media: () => queryClient.invalidateQueries({ queryKey: queryKeys.media.all }),
  auth: () => queryClient.invalidateQueries({ queryKey: queryKeys.auth.all }),
}
