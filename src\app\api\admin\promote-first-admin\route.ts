import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { supabaseAdmin } from '@/lib/supabase'
import { logger } from '@/lib/logger'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { email } = body

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 })
    }

    // Use the create_first_admin function from the database
    const { data, error } = await supabase.rpc('create_first_admin', { admin_email: email })

    if (error) {
      logger.error('Error promoting user to admin', error)
      return NextResponse.json({ error: 'Failed to promote user to admin', details: error.message }, { status: 500 })
    }

    if (!data.success) {
      return NextResponse.json({ error: data.error }, { status: 400 })
    }

    logger.info('Successfully promoted user to admin', { email })
    return NextResponse.json({ success: true, message: data.message })

  } catch (err) {
    const error = err instanceof Error ? err : new Error('An unexpected error occurred')
    logger.error('An unexpected error occurred in promote-first-admin API', error)
    return NextResponse.json({ error: 'Server error', details: error.message }, { status: 500 })
  }
}
