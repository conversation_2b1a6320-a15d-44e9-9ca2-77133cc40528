import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { supabaseAdmin } from '@/lib/supabase'
import { logger } from '@/lib/logger'

const updateSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  content: z.string().min(1, 'Content is required').optional(),
  excerpt: z.string().optional(),
  status: z.enum(['draft', 'published']).optional(),
  category_id: z.string().optional(),
  tag_ids: z.array(z.string()).optional(),
  order_index: z.number().int().min(0).optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: userRole } = await supabase.rpc('get_user_role', { user_id: session.user.id })

    if (userRole !== 'admin' && userRole !== 'editor') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Supabase admin client is not initialized.' }, { status: 500 })
    }

    const { data: article, error } = await supabaseAdmin
      .from('help_articles')
      .select(`
        *,
        author:users(name, email),
        category:categories(name, slug),
        help_article_tags(tag:tags(id, name, slug))
      `)
      .eq('id', params.id)
      .single()

    if (error) {
      logger.error('Error fetching help article', error)
      return NextResponse.json({ error: 'Failed to fetch help article' }, { status: 500 })
    }

    if (!article) {
      return NextResponse.json({ error: 'Help article not found' }, { status: 404 })
    }

    return NextResponse.json({ article })

  } catch (error) {
    logger.error('Unexpected error in help article GET', error as Error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: userRole } = await supabase.rpc('get_user_role', { user_id: session.user.id })

    if (userRole !== 'admin' && userRole !== 'editor') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validation = updateSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json({ error: 'Invalid data', details: validation.error.flatten() }, { status: 400 })
    }

    const { tag_ids, ...updateData } = validation.data

    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Supabase admin client is not initialized.' }, { status: 500 })
    }

    // Update the help article
    const { data: article, error: updateError } = await supabaseAdmin
      .from('help_articles')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', params.id)
      .select()
      .single()

    if (updateError) {
      logger.error('Error updating help article', updateError)
      return NextResponse.json({ error: 'Failed to update help article' }, { status: 500 })
    }

    // Update tags if provided
    if (tag_ids !== undefined) {
      // Remove existing tags
      await supabaseAdmin
        .from('help_article_tags')
        .delete()
        .eq('help_article_id', params.id)

      // Add new tags
      if (tag_ids.length > 0) {
        const tagRelations = tag_ids.map(tagId => ({
          help_article_id: params.id,
          tag_id: tagId
        }))

        const { error: tagError } = await supabaseAdmin
          .from('help_article_tags')
          .insert(tagRelations)

        if (tagError) {
          logger.error('Error updating help article tags', tagError)
        }
      }
    }

    return NextResponse.json({ article })

  } catch (error) {
    logger.error('Unexpected error in help article PUT', error as Error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: userRole } = await supabase.rpc('get_user_role', { user_id: session.user.id })

    if (userRole !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Supabase admin client is not initialized.' }, { status: 500 })
    }

    // Delete tag relations first
    await supabaseAdmin
      .from('help_article_tags')
      .delete()
      .eq('help_article_id', params.id)

    // Delete the help article
    const { error } = await supabaseAdmin
      .from('help_articles')
      .delete()
      .eq('id', params.id)

    if (error) {
      logger.error('Error deleting help article', error)
      return NextResponse.json({ error: 'Failed to delete help article' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Help article deleted successfully' })

  } catch (error) {
    logger.error('Unexpected error in help article DELETE', error as Error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
