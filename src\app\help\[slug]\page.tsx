'use client'

import { useState, useEffect, useCallback } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeft, Calendar, User, Tag, Eye, HelpCircle, Home, ChevronRight, Clock, FileText } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { HelpArticle } from '@/types'
import { formatDate } from '@/lib/utils'
import { Card, CardBody } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'

export default function HelpArticlePage() {
  const params = useParams()
  const slug = params.slug as string
  const [article, setArticle] = useState<HelpArticle | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchArticle = useCallback(async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('help_articles')
        .select(`
          *,
          author:users(name, email),
          category:categories(name, slug),
          tags:help_article_tags(tag:tags(name, slug))
        `)
        .eq('slug', slug)
        .eq('status', 'published')
        .single()

      if (error) throw error
      setArticle(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Article not found')
    } finally {
      setLoading(false)
    }
  }, [slug])

  useEffect(() => {
    if (slug) {
      fetchArticle()
    }
  }, [slug, fetchArticle])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-accent-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-neutral-600">Loading article...</p>
        </div>
      </div>
    )
  }

  if (error || !article) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-error-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <HelpCircle className="w-8 h-8 text-error-600" />
          </div>
          <h1 className="text-3xl font-bold text-neutral-900 mb-4">Article Not Found</h1>
          <p className="text-neutral-600 mb-8 max-w-md mx-auto">
            {error || 'The requested help article could not be found or may have been removed.'}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/help">
              <Button leftIcon={<ArrowLeft className="w-4 h-4" />}>
                Back to Help Center
              </Button>
            </Link>
            <Link href="/">
              <Button variant="secondary">
                Go Home
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-neutral-50">
      {/* Navigation Breadcrumb */}
      <div className="bg-white border-b border-neutral-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-neutral-500 hover:text-neutral-700 transition-colors">
              <Home className="w-4 h-4" />
            </Link>
            <ChevronRight className="w-4 h-4 text-neutral-400" />
            <Link href="/help" className="text-neutral-500 hover:text-neutral-700 transition-colors">
              Help Center
            </Link>
            <ChevronRight className="w-4 h-4 text-neutral-400" />
            <span className="text-neutral-900 font-medium truncate">{article.title}</span>
          </nav>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Article */}
        <article>
          <Card className="overflow-hidden">
            <CardBody className="p-8 lg:p-12">
              {/* Category and Meta */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                  {article.category && (
                    <Badge variant="success" size="lg">
                      <HelpCircle className="w-3 h-3 mr-1" />
                      {article.category.name}
                    </Badge>
                  )}
                  <div className="flex items-center text-sm text-neutral-500">
                    <Clock className="w-4 h-4 mr-1" />
                    <time dateTime={article.published_at || article.created_at}>
                      {formatDate(article.published_at || article.created_at)}
                    </time>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-accent-500 to-accent-600 rounded-xl flex items-center justify-center">
                  <FileText className="w-6 h-6 text-white" />
                </div>
              </div>

              {/* Title */}
              <h1 className="text-4xl lg:text-5xl font-bold text-neutral-900 mb-8 leading-tight">
                {article.title}
              </h1>

              {/* Author Information */}
              <div className="flex items-center space-x-4 mb-8 pb-8 border-b border-neutral-200">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-white" />
                </div>
                <div>
                  <p className="font-semibold text-neutral-900">
                    {article.author?.name || article.author?.email || 'Anonymous'}
                  </p>
                  <p className="text-sm text-neutral-500">
                    Published on {formatDate(article.published_at || article.created_at)}
                  </p>
                </div>
              </div>

              {/* Excerpt */}
              {article.excerpt && (
                <div className="text-xl text-neutral-600 mb-10 leading-relaxed font-light border-l-4 border-accent-500 pl-6 italic bg-accent-50 p-6 rounded-r-lg">
                  {article.excerpt}
                </div>
              )}

              {/* Content */}
              <div className="prose prose-lg prose-neutral max-w-none">
                <div className="whitespace-pre-wrap text-neutral-800 leading-relaxed">
                  {article.content}
                </div>
              </div>

              {/* Tags */}
              {article.tags && article.tags.length > 0 && (
                <div className="mt-12 pt-8 border-t border-neutral-200">
                  <div className="flex items-center flex-wrap gap-3">
                    <div className="flex items-center text-neutral-600 mr-2">
                      <Tag className="w-4 h-4 mr-2" />
                      <span className="text-sm font-medium">Tags:</span>
                    </div>
                    {article.tags.map((tagRelation: any) => (
                      <Badge
                        key={tagRelation.tag.slug}
                        variant="secondary"
                        size="sm"
                      >
                        {tagRelation.tag.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Help Actions */}
              <div className="mt-12 pt-8 border-t border-neutral-200">
                <Card className="bg-gradient-to-r from-accent-50 to-success-50 border-accent-200">
                  <CardBody>
                    <h3 className="text-lg font-semibold text-neutral-900 mb-3">Was this article helpful?</h3>
                    <p className="text-neutral-600 mb-6">
                      If you need additional assistance, please don&apos;t hesitate to contact our support team.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3">
                      <Button variant="accent" size="sm">
                        👍 Yes, helpful
                      </Button>
                      <Button variant="secondary" size="sm">
                        👎 No, not helpful
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              </div>
            </CardBody>
          </Card>
        </article>

        {/* Navigation */}
        <div className="mt-12 flex flex-col sm:flex-row gap-4 justify-between items-center">
          <Link href="/help">
            <Button variant="secondary" leftIcon={<ArrowLeft className="w-4 h-4" />}>
              Back to Help Center
            </Button>
          </Link>
          <Link href="/">
            <Button variant="ghost">
              Go Home
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}