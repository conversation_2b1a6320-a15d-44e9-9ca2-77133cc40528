export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          role: 'admin' | 'editor' | 'viewer'
          name: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          role?: 'admin' | 'editor' | 'viewer'
          name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          role?: 'admin' | 'editor' | 'viewer'
          name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          color: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          color?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          color?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      tags: {
        Row: {
          id: string
          name: string
          slug: string
          color: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          color?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          color?: string | null
          created_at?: string
        }
      }
      posts: {
        Row: {
          id: string
          title: string
          slug: string
          content: string
          excerpt: string | null
          featured_image: string | null
          status: 'draft' | 'published' | 'archived'
          author_id: string
          category_id: string | null
          published_at: string | null
          created_at: string
          updated_at: string
          version: number
        }
        Insert: {
          id?: string
          title: string
          slug: string
          content: string
          excerpt?: string | null
          featured_image?: string | null
          status?: 'draft' | 'published' | 'archived'
          author_id: string
          category_id?: string | null
          published_at?: string | null
          created_at?: string
          updated_at?: string
          version?: number
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          content?: string
          excerpt?: string | null
          featured_image?: string | null
          status?: 'draft' | 'published' | 'archived'
          author_id?: string
          category_id?: string | null
          published_at?: string | null
          created_at?: string
          updated_at?: string
          version?: number
        }
      }
      help_articles: {
        Row: {
          id: string
          title: string
          slug: string
          content: string
          excerpt: string | null
          status: 'draft' | 'published' | 'archived'
          author_id: string
          category_id: string | null
          order_index: number | null
          published_at: string | null
          created_at: string
          updated_at: string
          version: number
        }
        Insert: {
          id?: string
          title: string
          slug: string
          content: string
          excerpt?: string | null
          status?: 'draft' | 'published' | 'archived'
          author_id: string
          category_id?: string | null
          order_index?: number | null
          published_at?: string | null
          created_at?: string
          updated_at?: string
          version?: number
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          content?: string
          excerpt?: string | null
          status?: 'draft' | 'published' | 'archived'
          author_id?: string
          category_id?: string | null
          order_index?: number | null
          published_at?: string | null
          created_at?: string
          updated_at?: string
          version?: number
        }
      }
      media_files: {
        Row: {
          id: string
          filename: string
          original_name: string
          file_path: string
          file_size: number
          mime_type: string
          alt_text: string | null
          description: string | null
          folder_path: string | null
          download_count: number
          uploaded_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          filename: string
          original_name: string
          file_path: string
          file_size: number
          mime_type: string
          alt_text?: string | null
          description?: string | null
          folder_path?: string | null
          download_count?: number
          uploaded_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          filename?: string
          original_name?: string
          file_path?: string
          file_size?: number
          mime_type?: string
          alt_text?: string | null
          description?: string | null
          folder_path?: string | null
          download_count?: number
          uploaded_by?: string
          created_at?: string
          updated_at?: string
        }
      }
      content_versions: {
        Row: {
          id: string
          content_id: string
          content_type: 'post' | 'help_article'
          version_number: number
          title: string
          content: string
          changes_summary: string | null
          created_by: string
          created_at: string
        }
        Insert: {
          id?: string
          content_id: string
          content_type: 'post' | 'help_article'
          version_number: number
          title: string
          content: string
          changes_summary?: string | null
          created_by: string
          created_at?: string
        }
        Update: {
          id?: string
          content_id?: string
          content_type?: 'post' | 'help_article'
          version_number?: number
          title?: string
          content?: string
          changes_summary?: string | null
          created_by?: string
          created_at?: string
        }
      }
      post_tags: {
        Row: {
          post_id: string
          tag_id: string
        }
        Insert: {
          post_id: string
          tag_id: string
        }
        Update: {
          post_id?: string
          tag_id?: string
        }
      }
      help_article_tags: {
        Row: {
          help_article_id: string
          tag_id: string
        }
        Insert: {
          help_article_id: string
          tag_id: string
        }
        Update: {
          help_article_id?: string
          tag_id?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'admin' | 'editor' | 'viewer'
      content_status: 'draft' | 'published' | 'archived'
      content_type: 'post' | 'help_article'
    }
  }
}