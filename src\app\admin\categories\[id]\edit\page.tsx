'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Save, ArrowLeft, AlertCircle, Trash2 } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { CategoryFormData, Category } from '@/types'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuthContext } from '@/contexts/AuthContext'
import { generateSlug } from '@/lib/utils'

export default function EditCategoryPage() {
  const router = useRouter()
  const params = useParams()
  const { id } = params
  const { user } = useAuthContext()

  const [category, setCategory] = useState<Category | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    description: '',
    color: '#3B82F6'
  })

  const fetchCategoryData = useCallback(async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error

      setCategory(data)
      setFormData({
        name: data.name,
        description: data.description || '',
        color: data.color || '#3B82F6'
      })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch category data')
    } finally {
      setLoading(false)
    }
  }, [id])

  useEffect(() => {
    if (id) {
      fetchCategoryData()
    }
  }, [id, fetchCategoryData])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !category) return

    setLoading(true)
    setError(null)

    try {
      const slug = generateSlug(formData.name)

      const { data: existingCategory } = await supabase
        .from('categories')
        .select('id')
        .eq('slug', slug)
        .not('id', 'eq', category.id)
        .single()

      if (existingCategory) {
        throw new Error('A category with this name already exists.')
      }

      const { error: updateError } = await supabase
        .from('categories')
        .update({
          name: formData.name,
          slug,
          description: formData.description || null,
          color: formData.color,
          updated_at: new Date().toISOString()
        })
        .eq('id', category.id)

      if (updateError) throw updateError

      router.push('/admin/categories')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update category')
    } finally {
      setLoading(false)
    }
  }

  const deleteCategory = async () => {
    if (!category || !confirm('Are you sure you want to delete this category?')) return

    setLoading(true)
    try {
      const { error } = await supabase.from('categories').delete().eq('id', category.id)
      if (error) throw error
      router.push('/admin/categories')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete category')
      setLoading(false)
    }
  }

  if (loading && !category) {
    return <div>Loading...</div>
  }

  if (!category) {
    return <div>Category not found.</div>
  }

  return (
    <ProtectedRoute requiredRole={['admin', 'editor']}>
      <div className="max-w-2xl mx-auto">
        <div className="flex items-center mb-6">
          <button onClick={() => router.back()} className="flex items-center text-gray-600 hover:text-gray-900">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Categories
          </button>
        </div>

        <div className="bg-white p-8 rounded-lg shadow-md border border-gray-200">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Edit Category</h1>

          {error && (
            <div className="p-4 mb-6 bg-red-50 border border-red-200 rounded-lg flex items-start">
              <AlertCircle className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-red-800">{error}</p>
                <button onClick={() => setError(null)} className="mt-2 text-sm text-red-600 hover:text-red-800">
                  Dismiss
                </button>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">Category Name *</label>
              <input
                type="text"
                id="name"
                required
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                id="description"
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div>
              <label htmlFor="color" className="block text-sm font-medium text-gray-700 mb-2">Color</label>
              <div className="flex items-center space-x-3">
                <input
                  type="color"
                  id="color"
                  value={formData.color}
                  onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                  className="w-10 h-10 p-1 border border-gray-300 rounded-lg"
                />
                <input
                  type="text"
                  value={formData.color}
                  onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>

            <div className="flex justify-between items-center pt-4">
              <button
                type="button"
                onClick={deleteCategory}
                disabled={loading}
                className="flex items-center px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 disabled:opacity-50"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </button>
              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50"
                >
                  {loading ? 'Saving...' : 'Save Changes'}
                  {!loading && <Save className="w-4 h-4 ml-2" />}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </ProtectedRoute>
  )
}